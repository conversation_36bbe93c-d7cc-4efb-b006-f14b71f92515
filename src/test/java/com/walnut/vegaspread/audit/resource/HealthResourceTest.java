package com.walnut.vegaspread.audit.resource;

import io.quarkus.test.common.http.TestHTTPEndpoint;
import io.quarkus.test.junit.QuarkusTest;
import io.quarkus.test.security.TestSecurity;
import io.restassured.http.ContentType;
import org.junit.jupiter.api.Test;

import java.net.URI;

import static com.walnut.vegaspread.audit.resource.CommonTestUtils.NAME;
import static io.restassured.RestAssured.given;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.notNullValue;

@QuarkusTest
@TestHTTPEndpoint(HealthResource.class)
class HealthResourceTest {

    @Test
    void testReady() {
        given()
                .when()
                .get("/ready")
                .then()
                .statusCode(200)
                .contentType(ContentType.TEXT)
                .body(equalTo("OK"));
    }

    @Test
    void testBaseUrl() {
        URI baseUrl = given()
                .when()
                .get("/base-url")
                .then()
                .statusCode(200)
                .extract()
                .as(URI.class);

        // Verify that we get a valid URI back
        assert baseUrl != null;
        assert baseUrl.toString().contains("http");
    }

    @Test
    @TestSecurity(user = NAME)
    void testAuthenticated() {
        given()
                .when()
                .get("/authenticated")
                .then()
                .statusCode(200)
                .contentType(ContentType.TEXT)
                .body(equalTo("OK"));
    }

    @Test
    void testAppVersion() {
        given()
                .when()
                .get("/app-version")
                .then()
                .statusCode(200)
                .contentType(ContentType.TEXT)
                .body(notNullValue());
    }
}
