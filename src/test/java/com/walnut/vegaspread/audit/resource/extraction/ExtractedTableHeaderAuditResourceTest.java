package com.walnut.vegaspread.audit.resource.extraction;

import com.walnut.vegaspread.audit.service.extraction.ExtractedTableHeaderAuditService;
import io.quarkus.test.InjectMock;
import io.quarkus.test.common.http.TestHTTPEndpoint;
import io.quarkus.test.junit.QuarkusTest;
import io.quarkus.test.security.TestSecurity;
import io.restassured.http.ContentType;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import java.util.Collections;
import java.util.List;

import static com.walnut.vegaspread.audit.resource.CommonTestUtils.NAME;
import static io.restassured.RestAssured.given;
import static org.hamcrest.Matchers.hasSize;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.isNull;

@QuarkusTest
@TestHTTPEndpoint(ExtractedTableHeaderAuditResource.class)
class ExtractedTableHeaderAuditResourceTest {

    @InjectMock
    ExtractedTableHeaderAuditService extractedTableHeaderAuditService;

    @Test
    @TestSecurity(user = NAME)
    void testAuditForCreate() {
        Mockito.when(extractedTableHeaderAuditService.auditForCreate(anyList(), isNull()))
                .thenReturn(Collections.emptyList());

        given()
                .contentType(ContentType.JSON)
                .body(Collections.emptyList())
                .when()
                .post("/audit-create")
                .then()
                .statusCode(200)
                .contentType(ContentType.JSON)
                .body("", hasSize(0));

        Mockito.verify(extractedTableHeaderAuditService).auditForCreate(any(), isNull());
    }

    @Test
    @TestSecurity(user = NAME)
    void testAuditForUpdate() {
        Mockito.when(extractedTableHeaderAuditService.auditForUpdate(anyList(), isNull()))
                .thenReturn(Collections.emptyList());

        given()
                .contentType(ContentType.JSON)
                .body(Collections.emptyList())
                .when()
                .post("/audit-update")
                .then()
                .statusCode(200)
                .contentType(ContentType.JSON)
                .body("", hasSize(0));

        Mockito.verify(extractedTableHeaderAuditService).auditForUpdate(any(), isNull());
    }

    @Test
    @TestSecurity(user = NAME)
    void testAuditForDelete() {
        Mockito.when(extractedTableHeaderAuditService.auditForDelete(anyList(), isNull()))
                .thenReturn(Collections.emptyList());

        given()
                .contentType(ContentType.JSON)
                .body(Collections.emptyList())
                .when()
                .post("/audit-delete")
                .then()
                .statusCode(200)
                .contentType(ContentType.JSON)
                .body("", hasSize(0));

        Mockito.verify(extractedTableHeaderAuditService).auditForDelete(any(), isNull());
    }

    @Test
    @TestSecurity(user = NAME)
    void testListAuditsForBlocksAndCol() {
        String colName = "TestColumn";
        List<Integer> blockIds = List.of(1, 2, 3);

        Mockito.when(extractedTableHeaderAuditService.getColAuditsForTables(anyList(), anyString()))
                .thenReturn(Collections.emptyList());

        given()
                .contentType(ContentType.JSON)
                .body(blockIds)
                .when()
                .post("/" + colName + "/list")
                .then()
                .statusCode(200)
                .contentType(ContentType.JSON)
                .body("", hasSize(0));

        Mockito.verify(extractedTableHeaderAuditService).getColAuditsForTables(any(), any());
    }
}
