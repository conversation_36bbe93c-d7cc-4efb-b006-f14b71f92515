package com.walnut.vegaspread.audit.resource.extraction;

import com.walnut.vegaspread.audit.service.extraction.SubtotalService;
import io.quarkus.test.InjectMock;
import io.quarkus.test.common.http.TestHTTPEndpoint;
import io.quarkus.test.junit.QuarkusTest;
import io.quarkus.test.security.TestSecurity;
import io.restassured.http.ContentType;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import java.util.Collections;

import static com.walnut.vegaspread.audit.resource.CommonTestUtils.NAME;
import static io.restassured.RestAssured.given;
import static org.hamcrest.Matchers.hasSize;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.isNull;

@QuarkusTest
@TestHTTPEndpoint(SubtotalAuditResource.class)
class SubtotalAuditResourceTest {

    @InjectMock
    SubtotalService subtotalService;

    @Test
    @TestSecurity(user = NAME)
    void testAuditForCreate() {
        Mockito.when(subtotalService.auditForCreate(anyList()))
                .thenReturn(Collections.emptyList());

        given()
                .contentType(ContentType.JSON)
                .body(Collections.emptyList())
                .when()
                .post("/audit-create")
                .then()
                .statusCode(200)
                .contentType(ContentType.JSON)
                .body("", hasSize(0));

        Mockito.verify(subtotalService).auditForCreate(any());
    }

    @Test
    @TestSecurity(user = NAME)
    void testAuditForUpdate() {
        Mockito.when(subtotalService.auditForUpdate(anyList(), isNull()))
                .thenReturn(Collections.emptyList());

        given()
                .contentType(ContentType.JSON)
                .body(Collections.emptyList())
                .when()
                .post("/audit-update")
                .then()
                .statusCode(200)
                .contentType(ContentType.JSON)
                .body("", hasSize(0));

        Mockito.verify(subtotalService).auditForUpdate(any(), isNull());
    }

    @Test
    @TestSecurity(user = NAME)
    void testAuditForDelete() {
        Mockito.when(subtotalService.auditForDelete(anyList(), isNull()))
                .thenReturn(Collections.emptyList());

        given()
                .contentType(ContentType.JSON)
                .body(Collections.emptyList())
                .when()
                .post("/audit-delete")
                .then()
                .statusCode(200)
                .contentType(ContentType.JSON)
                .body("", hasSize(0));

        Mockito.verify(subtotalService).auditForDelete(any(), isNull());
    }
}
