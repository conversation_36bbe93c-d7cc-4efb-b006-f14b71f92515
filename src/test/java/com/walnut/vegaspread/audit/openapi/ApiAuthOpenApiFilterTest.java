package com.walnut.vegaspread.audit.openapi;

import org.eclipse.microprofile.openapi.OASFactory;
import org.eclipse.microprofile.openapi.models.OpenAPI;
import org.eclipse.microprofile.openapi.models.Operation;
import org.eclipse.microprofile.openapi.models.PathItem;
import org.eclipse.microprofile.openapi.models.Paths;
import org.eclipse.microprofile.openapi.models.parameters.Parameter;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

class ApiAuthOpenApiFilterTest {

    private ApiAuthOpenApiFilter filter;
    private OpenAPI openAPI;

    @BeforeEach
    void setUp() {
        filter = new ApiAuthOpenApiFilter();
        openAPI = OASFactory.createOpenAPI();
        
        // Create paths
        Paths paths = OASFactory.createPaths();
        
        // Create a path that should be filtered (contains /api-auth)
        PathItem apiAuthPathItem = OASFactory.createPathItem();
        Operation getOperation = OASFactory.createOperation();
        apiAuthPathItem.setGET(getOperation);
        paths.addPathItem("/api-auth/test", apiAuthPathItem);
        
        // Create a path that should NOT be filtered (doesn't contain /api-auth)
        PathItem regularPathItem = OASFactory.createPathItem();
        Operation postOperation = OASFactory.createOperation();
        regularPathItem.setPOST(postOperation);
        paths.addPathItem("/regular/test", regularPathItem);
        
        openAPI.setPaths(paths);
    }

    @Test
    void testFilterOpenAPI_AddsHeadersToApiAuthPaths() {
        filter.filterOpenAPI(openAPI);
        
        // Check that the api-auth path has the required headers
        PathItem apiAuthPath = openAPI.getPaths().getPathItem("/api-auth/test");
        assertNotNull(apiAuthPath);
        
        Operation getOperation = apiAuthPath.getGET();
        assertNotNull(getOperation);
        
        List<Parameter> parameters = getOperation.getParameters();
        assertNotNull(parameters);
        assertEquals(2, parameters.size());
        
        // Check for X-CLIENT-ID header
        Parameter clientIdParam = parameters.stream()
                .filter(p -> "X-CLIENT-ID".equals(p.getName()))
                .findFirst()
                .orElse(null);
        assertNotNull(clientIdParam);
        assertEquals("Client ID for authentication", clientIdParam.getDescription());
        assertTrue(clientIdParam.getRequired());
        assertEquals(Parameter.In.HEADER, clientIdParam.getIn());
        
        // Check for X-API-KEY header
        Parameter apiKeyParam = parameters.stream()
                .filter(p -> "X-API-KEY".equals(p.getName()))
                .findFirst()
                .orElse(null);
        assertNotNull(apiKeyParam);
        assertEquals("API KEY for authentication", apiKeyParam.getDescription());
        assertTrue(apiKeyParam.getRequired());
        assertEquals(Parameter.In.HEADER, apiKeyParam.getIn());
    }

    @Test
    void testFilterOpenAPI_DoesNotAddHeadersToRegularPaths() {
        filter.filterOpenAPI(openAPI);
        
        // Check that the regular path does NOT have the headers
        PathItem regularPath = openAPI.getPaths().getPathItem("/regular/test");
        assertNotNull(regularPath);
        
        Operation postOperation = regularPath.getPOST();
        assertNotNull(postOperation);
        
        List<Parameter> parameters = postOperation.getParameters();
        // Should be null or empty since no parameters were added
        assertTrue(parameters == null || parameters.isEmpty());
    }

    @Test
    void testFilterOpenAPI_WithMultipleOperations() {
        // Add multiple operations to the api-auth path
        PathItem apiAuthPathItem = openAPI.getPaths().getPathItem("/api-auth/test");
        Operation postOperation = OASFactory.createOperation();
        Operation putOperation = OASFactory.createOperation();
        apiAuthPathItem.setPOST(postOperation);
        apiAuthPathItem.setPUT(putOperation);
        
        filter.filterOpenAPI(openAPI);
        
        // Check that all operations have the headers
        PathItem filteredPath = openAPI.getPaths().getPathItem("/api-auth/test");
        
        // Check GET operation
        assertEquals(2, filteredPath.getGET().getParameters().size());
        
        // Check POST operation
        assertEquals(2, filteredPath.getPOST().getParameters().size());
        
        // Check PUT operation
        assertEquals(2, filteredPath.getPUT().getParameters().size());
    }

    @Test
    void testFilterOpenAPI_WithEmptyPaths() {
        OpenAPI emptyOpenAPI = OASFactory.createOpenAPI();
        emptyOpenAPI.setPaths(OASFactory.createPaths());
        
        // Should not throw any exception
        filter.filterOpenAPI(emptyOpenAPI);
        
        assertNotNull(emptyOpenAPI.getPaths());
    }
}
