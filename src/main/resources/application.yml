quarkus:
  native:
    resources:
      includes: clientSummaryTemplate/jsw_output.json

  smallrye-openapi:
    security-scheme: oauth2-implicit

  swagger-ui:
    always-include: true
    oauth-client-id: workflow-swagger-ui

  flyway:
    migrate-at-start: true

  hibernate-orm:
    physical-naming-strategy: org.hibernate.boot.model.naming.CamelCaseToUnderscoresNamingStrategy
    log:
      sql: false

  http:
    root-path: /vegaspread/api/v1/extraction
    proxy:
      proxy-address-forwarding: true
      enable-forwarded-host: true
      enable-forwarded-prefix: true
    cors:
      enabled: true
      origins: "*"
    access-log:
      enabled: true

org:
  eclipse:
    microprofile:
      rest:
        client:
          propagateHeaders: Authorization,X-CLIENT-ID,X-API-KEY

vega:
  env: ${VEGA_ENV}
