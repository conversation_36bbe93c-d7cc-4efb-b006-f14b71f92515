quarkus:
  smallrye-openapi:
    security-scheme: oauth2-implicit

  swagger-ui:
    always-include: true
    oauth-client-id: workflow-swagger-ui

  flyway:
    migrate-at-start: true
    placeholders:
      audit: ${vega.env}_vega_audit
      coa: ${vega.env}_vega_coa
      extraction: ${vega.env}_vega_extraction
      workflow: ${vega.env}_vega_workflow

  hibernate-orm:
    physical-naming-strategy: org.hibernate.boot.model.naming.CamelCaseToUnderscoresNamingStrategy

  http:
    root-path: /vegaspread/api/v1/audit
    proxy:
      proxy-address-forwarding: true
      enable-forwarded-host: true
      enable-forwarded-prefix: true
    cors:
      enabled: true
      origins: "*"
    access-log:
      enabled: true

org:
  eclipse:
    microprofile:
      rest:
        client:
          propagateHeaders: Authorization

vega:
  env: ${VEGA_ENV}
