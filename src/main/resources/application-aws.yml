quarkus:
  arc:
    exclude-types: com.walnut.vegaspread.common.security.AwsIamDbCredentialsProvider

  oidc:
    tls:
      tls-configuration-name: vega-tls

  tls:
    vega-tls:
      trust-store:
        p12:
          path: ssl/rcbc-truststore.p12
          password: 4Sge6qQgdmObp7IOqOyB9bp6bAiLFQQZ

  native:
    resources:
      includes: ssl/rcbc-truststore.p12

  keycloak:
    admin-client:
      server-url: ${KEYCLOAK_BASE_URL}
      client-id: admin-cli
      grant-type: CLIENT_CREDENTIALS
      tls-configuration-name: vega-tls

vega:
  cloud:
    provider: aws
    api-gateway-url: ${API_GATEWAY_URL}
    region: ${AURORA_INSTANCE_REGION:ap-southeast-1}
