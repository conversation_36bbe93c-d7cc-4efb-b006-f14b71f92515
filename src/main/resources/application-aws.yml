quarkus:
  datasource:
    credentials-provider: aws-iam-provider
    db-kind: postgresql
    username: ${POSTGRES_USERNAME}
    jdbc:
      url: jdbc:postgresql://${POSTGRES_HOST}:5432/${POSTGRES_DATABASE}

  oidc:
    tls:
      tls-configuration-name: vega-tls

  tls:
    vega-tls:
      trust-store:
        p12:
          path: ssl/rcbc-truststore.p12
          password: 4Sge6qQgdmObp7IOqOyB9bp6bAiLFQQZ

  rest-client:
    tls:
      tls-configuration-name: vega-tls

  native:
    resources:
      includes: ssl/rcbc-truststore.p12

  flyway:
    locations: classpath:db/migration/postgres
    schemas: ${VEGA_ENV}

  hibernate-orm:
    physical-naming-strategy: org.hibernate.boot.model.naming.CamelCaseToUnderscoresNamingStrategy
    database:
      default-schema: ${VEGA_ENV}

vega:
  cloud:
    provider: aws
    api-gateway-url: ${API_GATEWAY_URL}
    region: ${AURORA_INSTANCE_REGION:ap-southeast-1}
