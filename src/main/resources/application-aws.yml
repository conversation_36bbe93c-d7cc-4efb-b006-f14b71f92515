quarkus:
  datasource:
    credentials-provider: aws-iam-provider
    jdbc:
      url: jdbc:mysql://${AURORA_INSTANCE_HOST}:3306/${AURORA_INSTANCE_DATABASE}
      additional-jdbc-properties:
        sslMode=VERIFY_CA
    username: ${AURORA_INSTANCE_USERNAME}

  oidc:
    tls:
      tls-configuration-name: vega-tls

  tls:
    vega-tls:
      trust-store:
        p12:
          path: ssl/rcbc-truststore.p12
          password: 4Sge6qQgdmObp7IOqOyB9bp6bAiLFQQZ

  native:
    resources:
      includes: ssl/rcbc-truststore.p12

  rest-client:
    tls:
      tls-configuration-name: vega-tls

vega:
  cloud:
    provider: aws
    api-gateway-url: ${API_GATEWAY_URL}
    region: ${AURORA_INSTANCE_REGION:ap-southeast-1}
