quarkus:
  datasource:
    credentials-provider: aws-iam-provider
    jdbc:
      url: jdbc:mysql://${AURORA_INSTANCE_HOST}:3306/${AURORA_INSTANCE_DATABASE}
      additional-jdbc-properties:
        sslMode=VERIFY_CA
    username: ${AURORA_INSTANCE_USERNAME}

  oidc:
    tls:
      tls-configuration-name: vega-tls
  tls:
    vega-tls:
      trust-store:
        p12:
          path: ssl/rcbc-truststore.p12
          password: 4Sge6qQgdmObp7IOqOyB9bp6bAiLFQQZ

  native:
    resources:
      includes: ssl/rcbc-truststore.p12

  mailer:
    auth-methods: DIGEST-MD5 CRAM-SHA256 CRAM-SHA1 CRAM-MD5 PLAIN LOGIN
    from: <EMAIL>
    host: email-smtp.ap-southeast-1.amazonaws.com
    port: 25
    username: dev-vega-smtp-user
    password: BByZxNlI1C+udn9tikOwC5HkPOqoOEb0Sem9K6E6YbEU
    tls: true

vega:
  cloud:
    provider: aws
    api-gateway-url: ${API_GATEWAY_URL}

aws:
  ecs:
    task-family-name:
      rotation: ${ROTATION_TASK_FAMILY_NAME}
      ocr: ${OCR_TASK_FAMILY_NAME}
      docai: ${DOC_AI_TASK_FAMILY_NAME}
      processor: ${PROCESSOR_TASK_FAMILY_NAME}
      fs-clf: ${ FS_CLF_TASK_FAMILY_NAME}
      complete: ${COMPLETE_TASK_FAMILY_NAME}

    cpu:
      cluster: ${ECS_CPU_CLUSTER_NAME}
      security-groups: ${ECS_CPU_SECURITY_GROUP_ID}
    gpu:
      capacity-provider-name: ${ECS_GPU_CAPACITY_PROVIDER_NAME}
      cluster: ${ECS_GPU_CLUSTER_NAME}
    subnets: ${ECS_SUBNET_IDS}
  region: ap-southeast-1
  s3:
    bucket: ${S3_BUCKET_NAME}
    vpc-endpoint-hostname: ${S3_VPC_ENDPOINT_HOSTNAME}

  sqs:
    queue:
      url: ${SQS_QUEUE_URL}
