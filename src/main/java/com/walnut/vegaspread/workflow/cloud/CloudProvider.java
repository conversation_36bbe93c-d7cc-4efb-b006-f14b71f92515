// 1. CloudProvider interface
package com.walnut.vegaspread.workflow.cloud;

import com.walnut.vegaspread.workflow.model.WiseTaskNameDto;

import java.io.IOException;
import java.net.URI;
import java.net.URL;
import java.nio.file.Path;
import java.util.List;
import java.util.Map;

public interface CloudProvider {
    /**
     * Start a job in the cloud provider
     *
     * @param args          Arguments to pass to the job
     * @param jobName       Name of the job to execute
     * @param awsTaskFamily AWS task family, value ignored for google cloud
     * @return Execution ID of the started job
     */
    String startJob(List<String> args, String jobName, String awsTaskFamily, Map<String, String> envVars);

    /**
     * Get the execution status of a job
     *
     * @param jobName     Name of the job
     * @param executionId ID of the execution
     * @return true if execution succeeded, false if failed or still running
     * @throws InterruptedException if the thread is interrupted while waiting
     */
    void getExecutionStatus(String jobName, String executionId) throws InterruptedException;

    /**
     * Start a specialized service (like Document AI)
     *
     * @param dto DTO containing the service parameters
     * @return Execution ID
     */
    void startSpecializedService(Object dto);

    /**
     * Upload a file to the cloud
     *
     * @param objectPath Corresponds to the GCS path for Google Cloud Storage and S3 Key for AWS(Destination path).
     * @param filePath   The path for file to be uploaded
     */
    void upload(String objectPath, Path filePath) throws IOException;

    /**
     * Get the link to the file
     *
     * @param path    The path to the file
     * @param timeout The timeout for the link
     * @return The link for the file
     */
    URL getLink(String path, Integer timeout);

    /**
     * Delete the file from the cloud.
     *
     * @param filePath The path to the file
     */
    void delete(String filePath);

    /**
     * Download a file from the cloud
     *
     * @param path The path to the file
     * @return The contents of the file
     */
    byte[] download(String path);

    /**
     * Get the base URI for the cloud provider
     *
     * @param uriInfo Request URI information object.
     * @return The base URI for the cloud provider.
     */
    URI buildServiceBaseUrl();

    /**
     * Get a signed URL for uploading a file
     *
     * @param filePath The path to the file
     * @param timeout  The timeout for the link
     * @return The signed URL for uploading the file
     */
    URL getSignedPutUrl(String filePath, Integer timeout, String contentType);

    /**
     * Get the job name for the task
     *
     * @param taskName The name of the task
     * @return The job name for the task
     */
    String getJobName(WiseTaskNameDto taskName);

    String getBucketName();

    /**
     * @param taskName The name of the task
     * @return The env vars map for the task.
     */
    Map<String, String> getEnvVars(WiseTaskNameDto taskName);
}
