package com.walnut.vegaspread.workflow.cloud;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.walnut.vegaspread.common.model.cloud.CloudPlatform;
import com.walnut.vegaspread.common.utils.ConfigKeys;
import com.walnut.vegaspread.workflow.model.EcsRunTaskDto;
import com.walnut.vegaspread.workflow.model.WiseCallbackDto;
import com.walnut.vegaspread.workflow.model.WiseTaskNameDto;
import com.walnut.vegaspread.workflow.utils.Config;
import io.quarkus.arc.profile.IfBuildProfile;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import org.eclipse.microprofile.config.inject.ConfigProperty;
import org.jboss.logging.Logger;
import software.amazon.awssdk.auth.credentials.DefaultCredentialsProvider;
import software.amazon.awssdk.http.urlconnection.UrlConnectionHttpClient;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.ecs.EcsClient;
import software.amazon.awssdk.services.ecs.model.Container;
import software.amazon.awssdk.services.ecs.model.DescribeTasksRequest;
import software.amazon.awssdk.services.ecs.model.DescribeTasksResponse;
import software.amazon.awssdk.services.ecs.model.Task;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.model.DeleteObjectRequest;
import software.amazon.awssdk.services.s3.model.GetObjectRequest;
import software.amazon.awssdk.services.s3.model.PutObjectRequest;
import software.amazon.awssdk.services.s3.presigner.S3Presigner;
import software.amazon.awssdk.services.s3.presigner.model.GetObjectPresignRequest;
import software.amazon.awssdk.services.s3.presigner.model.PutObjectPresignRequest;
import software.amazon.awssdk.services.sqs.SqsClient;
import software.amazon.awssdk.services.sqs.model.SendMessageResponse;

import java.net.URI;
import java.net.URL;
import java.nio.file.Path;
import java.time.Duration;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@ApplicationScoped
@IfBuildProfile("aws")
public class AwsCloudProvider implements CloudProvider {
    private static final Logger logger = Logger.getLogger(AwsCloudProvider.class);
    private final EcsClient ecsClient;
    private final String cpuClusterName;
    private final List<String> subnets;
    private final List<String> securityGroups;
    private final String bucketName;
    private final S3Client s3Client;
    private final EcsHelper ecsHelper;
    private final SqsClient sqsClient;
    private final String sqsQueueUrl;
    private final String gpuClusterName;
    private final String docAiTaskFamilyName;
    private final String docAiCapacityProvider;
    private final String rootPath;
    private final String baseUrlString;
    private final S3Presigner s3Presigner;
    @ConfigProperty(name = ConfigKeys.ENV_NAME_KEY)
    String envName;

    @Inject
    public AwsCloudProvider(@ConfigProperty(name = ConfigKeys.AWS_REGION) Optional<String> region,
                            @ConfigProperty(name = Config.AWS_ECS_CPU_CLUSTER) String cpuClusterName,
                            @ConfigProperty(name = Config.AWS_ECS_GPU_CLUSTER) String gpuClusterName,
                            @ConfigProperty(name = Config.AWS_ECS_SUBNETS) Optional<List<String>> subnets,
                            @ConfigProperty(name = Config.AWS_ECS_SECURITY_GROUPS) Optional<List<String>> securityGroups,
                            @ConfigProperty(name = Config.AWS_S3_BUCKET) String bucketName,
                            @ConfigProperty(name = Config.AWS_SQS_QUEUE_URL) Optional<String> sqsQueueUrl,
                            @ConfigProperty(name = ConfigKeys.AWS_GATEWAY_URL) Optional<String> baseUrlString,
                            @ConfigProperty(name = Config.ROOT_PATH_URL, defaultValue = "/") String rootPath,
                            @ConfigProperty(name = Config.DOC_AI_TASK_FAMILY_NAME, defaultValue = "") String docAiTaskFamilyName,
                            @ConfigProperty(name = Config.DOC_AI_CAPACITY_PROVIDER_NAME, defaultValue = "") String docAiCapacityProvider,
                            @ConfigProperty(name = Config.AWS_S3_VPC_ENDPOINT_HOSTNAME, defaultValue = "") String s3VpcEndpoint,
                            EcsHelper ecsHelper
    ) {
        this.gpuClusterName = gpuClusterName;
        this.docAiTaskFamilyName = docAiTaskFamilyName;
        this.docAiCapacityProvider = docAiCapacityProvider;
        logger.debug("Root path for the aws " + rootPath);
        this.rootPath = rootPath;

        if (region.isEmpty() || subnets.isEmpty() || securityGroups.isEmpty() || sqsQueueUrl.isEmpty() || baseUrlString.isEmpty()) {
            throw new IllegalStateException(
                    "AWS gateway url, region, subnets, security groups or sql queue url are not configured");
        }
        this.ecsClient = EcsClient.builder()
                .region(Region.of(region.get()))
                .credentialsProvider(DefaultCredentialsProvider.create())
                .httpClient(UrlConnectionHttpClient.create())
                .build();
        this.sqsClient = SqsClient.builder()
                .region(Region.of(region.get()))
                .httpClient(UrlConnectionHttpClient.create())
                .build();
        this.cpuClusterName = cpuClusterName;
        this.subnets = subnets.get();
        this.securityGroups = securityGroups.get();
        this.bucketName = bucketName;
        this.s3Client = S3Client.builder()
                .region(Region.of(region.get()))
                .credentialsProvider(DefaultCredentialsProvider.create())
                .httpClient(UrlConnectionHttpClient.create())
                .build();
        this.ecsHelper = ecsHelper;
        this.sqsQueueUrl = sqsQueueUrl.get();
        this.baseUrlString = baseUrlString.get();
        this.s3Presigner = S3Presigner.builder()
                .endpointOverride(URI.create(s3VpcEndpoint))
                .region(Region.of(region.get()))
                .credentialsProvider(DefaultCredentialsProvider.create())
                .build();
    }

    public String startJob(List<String> args, String jobName, String awsTaskFamily, Map<String, String> envVars) {
        try {
            logger.infof("Adding task to queue for job: %s, task family: %s", jobName, awsTaskFamily);

            // Get the latest task definition
            String awsTaskDefinition;
            String taskContainerName = awsTaskFamily.replace("task", "container");
            try {
                awsTaskDefinition = ecsHelper.getLatestRevisionForContainer(awsTaskFamily, taskContainerName);
                logger.debugf("Ecs task definition for job %s is: %s", jobName, awsTaskDefinition);
            } catch (Exception e) {
                logger.errorf("Failed to get latest task definition for job %s, family %s: %s",
                        jobName, awsTaskFamily, e.getMessage());
                throw new RuntimeException("Failed to get latest task definition", e);
            }

            //Serialize the RunTaskRequest to JSON
            EcsRunTaskDto dto = new EcsRunTaskDto();
            dto.cluster = cpuClusterName;
            dto.taskDefinition = awsTaskDefinition;
            dto.launchType = "FARGATE";

            // Network configuration
            dto.networkConfiguration = new EcsRunTaskDto.NetworkConfiguration();
            dto.networkConfiguration.awsvpcConfiguration = new EcsRunTaskDto.NetworkConfiguration.AwsVpcConfiguration();
            dto.networkConfiguration.awsvpcConfiguration.assignPublicIp = "ENABLED";
            dto.networkConfiguration.awsvpcConfiguration.subnets = subnets;
            dto.networkConfiguration.awsvpcConfiguration.securityGroups = securityGroups;

            // Environment variables
            List<EcsRunTaskDto.Overrides.ContainerOverride.EnvironmentVariable> envList = envVars.entrySet()
                    .stream()
                    .map(e -> {
                        var env = new EcsRunTaskDto.Overrides.ContainerOverride.EnvironmentVariable();
                        env.name = e.getKey();
                        env.value = e.getValue();
                        return env;
                    }).toList();

            // Container override
            EcsRunTaskDto.Overrides.ContainerOverride containerOverride =
                    new EcsRunTaskDto.Overrides.ContainerOverride();
            containerOverride.name = taskContainerName;
            containerOverride.command = args;
            containerOverride.environment = envList;

            dto.overrides = new EcsRunTaskDto.Overrides();
            dto.overrides.containerOverrides = List.of(containerOverride);

            // Serialize
            ObjectMapper mapper = new ObjectMapper();
            mapper.enable(SerializationFeature.WRAP_ROOT_VALUE);
            String json = mapper.writeValueAsString(dto);

            // Send the message to SQS
            logger.debugf("Sending task request to SQS queue: %s", sqsQueueUrl);
            try {
                // Generate a unique message group ID for FIFO queues for each job

                SendMessageResponse response = sqsClient.sendMessage(
                        builder -> builder.queueUrl(sqsQueueUrl)
                                .messageBody(json)
                                .messageGroupId(jobName));

                // Get the message ID from the response
                String messageId = response.messageId();
                logger.infof("Successfully added task to queue for job %s. Message ID: %s, Group ID: %s",
                        jobName, messageId, jobName);
                return messageId;
            } catch (Exception e) {
                logger.errorf("Failed to send message to SQS queue: %s", e.getMessage());
                throw new RuntimeException("Failed to send message to SQS queue", e);
            }
        } catch (Exception e) {
            logger.errorf("Error in addToQueue for job %s: %s", jobName, e.getMessage());
            throw new RuntimeException("Failed to add task to queue: " + e.getMessage(), e);
        }
    }

    @Override
    public void startSpecializedService(Object obj) {
        // Handle Document AI
        if (obj instanceof WiseCallbackDto.DocAiDto docAiDto) {
            String jobName = String.format(getJobName(WiseTaskNameDto.DOC_AI), this.envName);
            String awsTaskFamily = String.format(docAiTaskFamilyName, this.envName);
            List<String> args = docAiDto.generateArgs();
            Map<String, String> envVars = Collections.emptyMap();

            try {
                logger.infof("Adding task to queue for job: %s, task family: %s", jobName, awsTaskFamily);

                // Get the latest task definition
                String awsTaskDefinition;
                String taskContainerName = awsTaskFamily.replace("task", "container");
                try {
                    awsTaskDefinition = ecsHelper.getLatestRevisionForContainer(awsTaskFamily, taskContainerName);
                    logger.debugf("Ecs task definition for job %s is: %s", jobName, awsTaskDefinition);
                } catch (Exception e) {
                    logger.errorf("Failed to get latest task definition for job %s, family %s: %s",
                            jobName, awsTaskFamily, e.getMessage());
                    throw new RuntimeException("Failed to get latest task definition", e);
                }

                //Serialize the RunTaskRequest to JSON
                EcsRunTaskDto dto = new EcsRunTaskDto();
                dto.cluster = gpuClusterName;
                dto.taskDefinition = awsTaskDefinition;

                // Create capacity provider strategy as a list
                EcsRunTaskDto.Strategy strategy = new EcsRunTaskDto.Strategy();
                strategy.capacityProvider = docAiCapacityProvider;
                strategy.weight = 1;
                strategy.base = 0;
                dto.capacityProviderStrategy = List.of(strategy);

                // Network configuration
                dto.networkConfiguration = new EcsRunTaskDto.NetworkConfiguration();
                dto.networkConfiguration.awsvpcConfiguration =
                        new EcsRunTaskDto.NetworkConfiguration.AwsVpcConfiguration();
                dto.networkConfiguration.awsvpcConfiguration.assignPublicIp = "DISABLED";
                dto.networkConfiguration.awsvpcConfiguration.subnets = subnets;

                // Environment variables
                List<EcsRunTaskDto.Overrides.ContainerOverride.EnvironmentVariable> envList = envVars.entrySet()
                        .stream()
                        .map(e -> {
                            var env = new EcsRunTaskDto.Overrides.ContainerOverride.EnvironmentVariable();
                            env.name = e.getKey();
                            env.value = e.getValue();
                            return env;
                        }).toList();

                // Container override
                EcsRunTaskDto.Overrides.ContainerOverride containerOverride =
                        new EcsRunTaskDto.Overrides.ContainerOverride();
                containerOverride.name = taskContainerName;
                containerOverride.command = args;
                containerOverride.environment = envList;

                dto.overrides = new EcsRunTaskDto.Overrides();
                dto.overrides.containerOverrides = List.of(containerOverride);

                // Serialize
                ObjectMapper mapper = new ObjectMapper();
                mapper.enable(SerializationFeature.WRAP_ROOT_VALUE);
                String json = mapper.writeValueAsString(dto);

                // Send the message to SQS
                logger.debugf("Sending task request to SQS queue: %s", sqsQueueUrl);
                try {
                    // Generate a unique message group ID for FIFO queues for each job

                    SendMessageResponse response = sqsClient.sendMessage(
                            builder -> builder.queueUrl(sqsQueueUrl)
                                    .messageBody(json)
                                    .messageGroupId(jobName));

                    // Get the message ID from the response
                    String messageId = response.messageId();
                    logger.infof("Successfully added task to queue for job %s. Message ID: %s, Group ID: %s",
                            jobName, messageId, jobName);
                    logger.debugf("Started Document AI service for docId: %s, execution id: %s",
                            docAiDto.getDocId(), messageId);
                } catch (Exception e) {
                    logger.errorf("Failed to send message to SQS queue: %s", e.getMessage());
                    throw new RuntimeException("Failed to send message to SQS queue", e);
                }
            } catch (Exception e) {
                logger.errorf("Error in addToQueue for job %s: %s", jobName, e.getMessage());
                throw new RuntimeException("Failed to add task to queue: " + e.getMessage(), e);
            }
        } else {
            throw new IllegalArgumentException("Unsupported DTO type: " + obj.getClass().getName());
        }
    }

    @Override
    public void getExecutionStatus(String jobName, String executionId) throws InterruptedException {
        for (long i = 1; i < 20; i++) {
            logger.infof("Checking task status: %s", executionId);

            // Call AWS SDK `describeTasks` API
            DescribeTasksResponse response = ecsClient.describeTasks(DescribeTasksRequest.builder()
                    .cluster(cpuClusterName)
                    .tasks(executionId)
                    .build());

            List<Task> tasks = response.tasks();

            if (tasks.isEmpty()) {
                logger.errorf("Task not found: %s", executionId);
                break;
            }

            Task task = tasks.get(0);
            String lastStatus = task.lastStatus();

            if ("STOPPED".equals(lastStatus)) {
                // Check container exit codes
                for (Container container : task.containers()) {
                    if (container.exitCode() != null && container.exitCode() != 0) {
                        logger.errorf("Task failed with exit code: %d, reason: %s",
                                container.exitCode(), container.reason());
                        break;
                    }
                }
                logger.infof("Task completed successfully: %s", executionId);
                break;
            } else if ("RUNNING".equals(lastStatus) || "PROVISIONING".equals(lastStatus) || "PENDING".equals(
                    lastStatus)) {
                logger.infof("Task still in progress: %s, status: %s", executionId, lastStatus);
                Thread.sleep(2000 * i);
            } else {
                logger.errorf("Task in unexpected state: %s, status: %s", executionId, lastStatus);
                break;
            }
        }

        logger.warnf("Task status check timed out: %s", executionId);
    }

    @Override
    public void upload(String s3Key, Path filePath) {
        logger.debugf("Uploading file to %s in S3", s3Key);

        PutObjectRequest putRequest = PutObjectRequest.builder()
                .bucket(bucketName)
                .key(s3Key)
                .build();

        s3Client.putObject(putRequest, filePath);
        logger.debugf("Finished uploading file to %s in S3", s3Key);
    }

    @Override
    public URL getLink(String filePath, Integer timeoutInMinutes) {
        GetObjectRequest getObjectRequest = GetObjectRequest.builder()
                .bucket(bucketName)
                .key(filePath)
                .build();

        GetObjectPresignRequest presignRequest = GetObjectPresignRequest.builder()
                .signatureDuration(Duration.ofMinutes(timeoutInMinutes))
                .getObjectRequest(getObjectRequest)
                .build();

        return s3Presigner.presignGetObject(presignRequest).url();
    }

    @Override
    public void delete(String filePath) {
        DeleteObjectRequest deleteRequest = DeleteObjectRequest.builder()
                .bucket(bucketName)
                .key(filePath)
                .build();

        s3Client.deleteObject(deleteRequest);
    }

    @Override
    public byte[] download(String path) {
        GetObjectRequest getRequest = GetObjectRequest.builder()
                .bucket(bucketName)
                .key(path)
                .build();
        return s3Client.getObjectAsBytes(getRequest).asByteArray();
    }

    @Override
    public URI buildServiceBaseUrl() {
        String fullBaseUri = baseUrlString + rootPath + "/";
        logger.debug("Full base Url: " + fullBaseUri);
        return URI.create(fullBaseUri);
    }

    @Override
    public URL getSignedPutUrl(String filePath, Integer timeoutInMinutes, String mimeType) {

        PutObjectRequest putObjectRequest = PutObjectRequest.builder()
                .bucket(bucketName)
                .key(filePath)
                .build();

        PutObjectPresignRequest presignRequest = PutObjectPresignRequest.builder()
                .signatureDuration(Duration.ofMinutes(timeoutInMinutes))
                .putObjectRequest(putObjectRequest)
                .build();

        return s3Presigner.presignPutObject(presignRequest).url();
    }

    @Override
    public String getJobName(WiseTaskNameDto taskName) {
        return switch (taskName) {
            case ROTATION -> "%s-vega-cpu-rotation-task";
            case OCR -> "%s-vega-cpu-ocr-task";
            case DOC_AI -> "%s-vega-gpu-docai-task";
            case FS_CLF -> "%s-vega-cpu-fs-classification-task";
            case PROCESSING -> "%s-vega-cpu-processing-task";
            case COMPLETE -> "%s-vega-cpu-complete-task";
            default -> throw new IllegalArgumentException("Unsupported task name: " + taskName);
        };
    }

    @Override
    public String getBucketName() {
        return this.bucketName;
    }

    @Override
    public Map<String, String> getEnvVars(WiseTaskNameDto taskName) {
        Map<String, String> envVars = new HashMap<>();
        return switch (taskName) {
            case DOC_AI, ROTATION, OCR, FS_CLF -> Collections.emptyMap();
            case PROCESSING, COMPLETE -> {
                envVars.put("BASE_URL", buildServiceUrlTemplate());
                yield envVars;
            }
            default -> throw new IllegalArgumentException("Unsupported task name: " + taskName);
        };
    }

    private String buildServiceUrlTemplate() {
        String baseUriString;
        String postFixServiceUrl;
        baseUriString = buildServiceBaseUrl().toString();
        postFixServiceUrl = CloudPlatform.AWS.getBaseUrlPostfixServicePath();

        //Remove "workflow" from the end of the url and replace other mentions of workflow with %s
        return baseUriString.substring(0, baseUriString.indexOf(postFixServiceUrl))
                .replace("workflow", "%s");
    }
}
