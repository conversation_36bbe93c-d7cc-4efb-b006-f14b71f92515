// 3. GCP Cloud Run Implementation
package com.walnut.vegaspread.workflow.cloud;

import com.google.cloud.storage.BlobId;
import com.google.cloud.storage.BlobInfo;
import com.google.cloud.storage.HttpMethod;
import com.google.cloud.storage.Storage;
import com.google.cloud.storage.StorageOptions;
import com.walnut.vegaspread.common.model.cloud.CloudPlatform;
import com.walnut.vegaspread.common.utils.ConfigKeys;
import com.walnut.vegaspread.workflow.client.GcpCloudRunClient;
import com.walnut.vegaspread.workflow.client.RunpodClient;
import com.walnut.vegaspread.workflow.model.GcpCloudRunDto;
import com.walnut.vegaspread.workflow.model.RunpodDto;
import com.walnut.vegaspread.workflow.model.WiseCallbackDto;
import com.walnut.vegaspread.workflow.model.WiseTaskNameDto;
import com.walnut.vegaspread.workflow.service.GcpAuthService;
import com.walnut.vegaspread.workflow.utils.Config;
import io.quarkus.arc.profile.UnlessBuildProfile;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import org.eclipse.microprofile.config.inject.ConfigProperty;
import org.eclipse.microprofile.rest.client.inject.RestClient;
import org.jboss.logging.Logger;

import java.io.IOException;
import java.net.URI;
import java.net.URL;
import java.nio.file.Path;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

import static com.walnut.vegaspread.workflow.utils.Config.CB_API_KEY_NAME;
import static com.walnut.vegaspread.workflow.utils.Config.WISE_API_ENV_NAME;

@ApplicationScoped
@UnlessBuildProfile("aws")
public class GcpCloudProvider implements CloudProvider {
    private static final Logger logger = Logger.getLogger(GcpCloudProvider.class);

    private final GcpCloudRunClient gcpCloudRunClient;
    private final RunpodClient runpodClient;
    private final Storage gcStorage;
    private final String bucketName;
    private final String clientSecret;
    private final String hostUrlString;
    private final String rootPath;
    private final GcpAuthService gcpAuthService;
    private String authHeader;

    @Inject
    public GcpCloudProvider(
            @RestClient GcpCloudRunClient gcpCloudRunClient,
            @RestClient RunpodClient runpodClient,
            @ConfigProperty(name = ConfigKeys.ENV_NAME_KEY) String envName,
            GcpAuthService gcpAuthService,
            @ConfigProperty(name = Config.WISE_CLIENT_SECRET_KEY) String clientSecret,
            @ConfigProperty(name = Config.GCP_HOST_URL) Optional<String> hostUrlString,
            @ConfigProperty(name = Config.ROOT_PATH_URL, defaultValue = "/") String rootPath,
            @ConfigProperty(name = Config.VEGA_BUCKET_NAME) String bucketName) {
        if (hostUrlString.isEmpty()) {
            throw new IllegalStateException("Host url environment variable is not configured");
        }
        if (bucketName.isEmpty()) {
            throw new IllegalStateException("Bucket name environment variable is not configured");
        }
        this.gcpCloudRunClient = gcpCloudRunClient;
        this.runpodClient = runpodClient;
        this.bucketName = bucketName;
        this.clientSecret = clientSecret;
        this.hostUrlString = hostUrlString.get();
        this.rootPath = rootPath;
        this.gcStorage = StorageOptions.getDefaultInstance().getService();
        this.gcpAuthService = gcpAuthService;
    }

    @Override
    public String startJob(List<String> args, String jobName, String awsTaskFamily, Map<String, String> envVars) {

        // Create a list of environment variables
        List<GcpCloudRunDto.OverrideEnvVar> envVarList = new ArrayList<>();

        // Add all environment variables from the map
        for (Map.Entry<String, String> entry : envVars.entrySet()) {
            envVarList.add(new GcpCloudRunDto.OverrideEnvVar(entry.getKey(), entry.getValue()));
        }

        // Create container override
        GcpCloudRunDto.ExecuteContainerOverride executeContainerOverride =
                new GcpCloudRunDto.ExecuteContainerOverride(
                        "",
                        args,
                        envVarList);

        // Create execute override
        GcpCloudRunDto.ExecuteOverride executeOverride =
                new GcpCloudRunDto.ExecuteOverride(executeContainerOverride);

        // Create request body
        GcpCloudRunDto.ExecuteRequestBody requestBody =
                new GcpCloudRunDto.ExecuteRequestBody(
                        Boolean.FALSE,
                        "",
                        executeOverride);
        authHeader = "Bearer " + gcpAuthService.getGcpAccessToken();
        // Execute job
        GcpCloudRunDto.ExecuteOperation operation =
                gcpCloudRunClient.executeJob(
                        jobName,
                        requestBody,
                        authHeader);

        // Extract execution ID
        String[] executionName = operation.metadata().name().split("/");
        return executionName[executionName.length - 1];
    }

    @Override
    public void getExecutionStatus(String jobName, String executionId) throws InterruptedException {
        authHeader = "Bearer " + gcpAuthService.getGcpAccessToken();
        for (long i = 1; i < 20; i++) {
            logger.infof("Checking task status for %s, execution id: %s", jobName, executionId);
            GcpCloudRunDto.ExecutionStatus executionStatus =
                    gcpCloudRunClient.getExecution(
                            jobName,
                            executionId,
                            authHeader);

            Integer succeededCount = executionStatus.succeededCount();
            Integer failedCount = executionStatus.failedCount();
            if (succeededCount != null || failedCount != null) {
                if (failedCount != null) {
                    logger.errorf("Task failed for %s, execution id: %s", jobName, executionId);
                } else {
                    logger.infof("Task succeeded for %s, execution id: %s", jobName, executionId);
                }
                break;
            }

            Thread.sleep(2000 * i);
        }
    }

    @Override
    public void startSpecializedService(Object dto) {
        // Handle Document AI using Runpod
        if (dto instanceof WiseCallbackDto.DocAiDto docAiDto) {
            // Start Runpod job
            RunpodDto.StartJobRsp response = runpodClient.startJob(docAiDto.generatePayload());
            logger.infof("Started Document AI service for docId: %s, execution id: %s",
                    docAiDto.getDocId(), response.id());
        } else {
            throw new IllegalArgumentException("Unsupported DTO type: " + dto.getClass().getName());
        }
    }

    @Override
    public void upload(String gcsPath, Path filePath) throws IOException {
        logger.debugf("Uploading file to %s in cloud storage", gcsPath);
        BlobInfo blobInfo = BlobInfo.newBuilder(BlobId.of(bucketName, gcsPath)).build();

        gcStorage.createFrom(blobInfo, filePath);
        logger.debugf("Finished uploading file to %s in cloud storage", gcsPath);
    }

    @Override
    public URL getLink(String gcsPath, Integer timeoutInMinutes) {
        BlobInfo blobInfo = BlobInfo.newBuilder(BlobId.of(bucketName, gcsPath)).build();
        return gcStorage.signUrl(blobInfo, timeoutInMinutes, TimeUnit.MINUTES,
                Storage.SignUrlOption.withV4Signature());
    }

    @Override
    public void delete(String filePath) {
        gcStorage.delete(bucketName, filePath);
    }

    @Override
    public byte[] download(String path) {
        return gcStorage.readAllBytes(bucketName, path);
    }

    @Override
    public URI buildServiceBaseUrl() {
        String fullBaseUri = hostUrlString + rootPath + "/";
        logger.debugf("Full base Url: " + fullBaseUri);
        return URI.create(fullBaseUri);
    }

    @Override
    public URL getSignedPutUrl(String filePath, Integer timeoutInMinutes, String contentType) {
        BlobInfo blobInfo = BlobInfo.newBuilder(BlobId.of(bucketName, filePath)).setContentType(contentType).build();
        return gcStorage.signUrl(blobInfo, timeoutInMinutes, TimeUnit.MINUTES,
                Storage.SignUrlOption.withV4Signature(),
                Storage.SignUrlOption.httpMethod(HttpMethod.PUT));
    }

    @Override
    public String getJobName(WiseTaskNameDto taskName) {
        return switch (taskName) {
            case ROTATION -> "%s-wise-ocr-rotate";
            case OCR -> "%s-wise-ocr";
            case DOC_AI -> "vega-document-ai";
            case FS_CLF -> "%s-vega-fs-classification";
            case PROCESSING -> "%s-vega-processor-process-job";
            case COMPLETE -> "%s-vega-processor-complete-job";
        };
    }

    @Override
    public String getBucketName() {
        return this.bucketName;
    }

    @Override
    public Map<String, String> getEnvVars(WiseTaskNameDto taskName) {
        Map<String, String> envVars = new HashMap<>();
        logger.debugf("Env vars for " + taskName.name());
        switch (taskName) {
            case ROTATION, OCR, FS_CLF -> {
                envVars.put(CB_API_KEY_NAME, this.clientSecret);
                logger.debugf(envVars.toString());
                return envVars;
            }

            case PROCESSING, COMPLETE -> {
                String tokenUrl = buildServiceBaseUrl().toString()
                        .replace("workflow", "iam") + "api/generate-token";
                logger.debugf("Token Url " + tokenUrl);
                envVars.put(WISE_API_ENV_NAME, this.clientSecret);
                envVars.put("BASE_URL", buildServiceUrlTemplate());
                envVars.put("TOKEN_URL", tokenUrl);
                logger.debugf(envVars.toString());
                return envVars;
            }
            default -> {
                return envVars;
            }
        }
    }

    private String buildServiceUrlTemplate() {

        String serviceBaseUrlString = buildServiceBaseUrl().toString();
        String postFixServiceUrl = CloudPlatform.GCP.getBaseUrlPostfixServicePath();

        //Remove "workflow" from the end of the url and replace other mentions of workflow with %s
        return serviceBaseUrlString.substring(0, serviceBaseUrlString.indexOf(postFixServiceUrl))
                .replace("workflow", "%s");
    }
}
