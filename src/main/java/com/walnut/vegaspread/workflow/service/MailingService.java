package com.walnut.vegaspread.workflow.service;

import com.walnut.vegaspread.workflow.entity.Document;
import com.walnut.vegaspread.workflow.entity.JobExecutionMonitoring;
import com.walnut.vegaspread.workflow.model.JobSummary;
import io.quarkus.mailer.MailTemplate;
import io.quarkus.qute.CheckedTemplate;
import jakarta.enterprise.context.ApplicationScoped;
import org.jboss.logging.Logger;

import java.time.Duration;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.UUID;

@ApplicationScoped
public class MailingService {
    private static final Logger logger = Logger.getLogger(MailingService.class);
    private static final int EMAIL_TIMEOUT_SECONDS = 10;

    private LocalDateTime convertUtcToUtcPlus8(LocalDateTime utcTime) {
        ZonedDateTime utcZoned = utcTime.atZone(ZoneId.of("UTC"));
        ZonedDateTime utcPlus8Zoned = utcZoned.withZoneSameInstant(ZoneId.of("UTC+08:00"));
        return utcPlus8Zoned.toLocalDateTime();
    }

    private String generateMailSubject(Document doc, String status) {
        return String.format("Document %s for %s for FY%s", status, doc.getSpreadingTask().getEntityName().getName(),
                doc.getPeriod().getYear());
    }

    private void logMailSuccess(String recipientEmail, UUID docId) {
        logger.infof("Email successfully sent to %s for docId: %s", recipientEmail, docId);
    }

    private void logMailFailure(String recipientEmail, UUID docId, Exception exception) {
        String errorMessage = String.format("Failed to send email to %s for docId: %s", recipientEmail, docId);
        logger.errorf(errorMessage);
        throw new RuntimeException(errorMessage, exception);
    }

    public void sendJobSummaryEmail(List<JobExecutionMonitoring> jobs, String recipientEmail, String docUrl,
                                    Document doc) {
        if (jobs == null || jobs.isEmpty()) {
            logger.warnf("No job telemetry found");
            return;
        }
        jobs.sort(Comparator.comparing(JobExecutionMonitoring::getStartTime));
        UUID docId = doc.getDocId();
        List<JobSummary> summaries = new ArrayList<>();
        long totalSeconds = 0;

        for (JobExecutionMonitoring job : jobs) {
            logger.debugf("Job: %s", job);
            Duration duration = Duration.between(job.getStartTime(), job.getEndTime());
            totalSeconds += duration.getSeconds();
            summaries.add(new JobSummary(
                    job.getStage().toString(),
                    convertUtcToUtcPlus8(job.getStartTime()),
                    convertUtcToUtcPlus8(job.getEndTime()),
                    duration.toMinutes(),
                    duration.toSecondsPart()
            ));
        }

        long totalMinutes = totalSeconds / 60;
        long totalRemainderSeconds = totalSeconds % 60;

        try {
            Templates.jobSummary(summaries, totalMinutes, totalRemainderSeconds, docUrl, docId)
                    .subject(generateMailSubject(doc, "finished"))
                    .to(recipientEmail).send()
                    .await().atMost(Duration.ofSeconds(EMAIL_TIMEOUT_SECONDS));

            logMailSuccess(recipientEmail, docId);
        } catch (Exception e) {
            logMailFailure(recipientEmail, docId, e);
        }
    }

    public void sendReprocessCompleteEmail(Document doc, String recipientEmail, String docUrl) {
        UUID docId = doc.getDocId();
        try {
            Templates.reprocessComplete(docUrl, docId)
                    .subject(generateMailSubject(doc, "reprocessed"))
                    .to(recipientEmail).send()
                    .await().atMost(Duration.ofSeconds(EMAIL_TIMEOUT_SECONDS));
            logMailSuccess(recipientEmail, docId);
        } catch (Exception e) {
            logMailFailure(recipientEmail, docId, e);
        }
    }

    public void sendFailedDocProcessingEmail(List<JobExecutionMonitoring> jobs, String recipientEmail, String docUrl,
                                             Document doc) {
        if (jobs == null || jobs.isEmpty()) {
            logger.warnf("No job telemetry found");
            return;
        }
        jobs.sort(Comparator.comparing(JobExecutionMonitoring::getStartTime));
        UUID docId = doc.getDocId();
        List<JobSummary> summaries = new ArrayList<>();
        long totalSeconds = 0;

        for (JobExecutionMonitoring job : jobs) {
            logger.debugf("Job: %s", job);
            Duration duration = Duration.between(job.getStartTime(), job.getEndTime());
            totalSeconds += duration.getSeconds();
            summaries.add(new JobSummary(
                    job.getStage().toString(),
                    convertUtcToUtcPlus8(job.getStartTime()),
                    convertUtcToUtcPlus8(job.getEndTime()),
                    duration.toMinutes(),
                    duration.toSecondsPart()
            ));
        }

        long totalMinutes = totalSeconds / 60;
        long totalRemainderSeconds = totalSeconds % 60;

        try {
            Templates.failedDocProcessing(summaries, totalMinutes, totalRemainderSeconds, docUrl, docId)
                    .subject(generateMailSubject(doc, "failed"))
                    .to(recipientEmail).send()
                    .await().atMost(Duration.ofSeconds(EMAIL_TIMEOUT_SECONDS));

            logMailSuccess(recipientEmail, docId);
        } catch (Exception e) {
            logMailFailure(recipientEmail, docId, e);
        }
    }

    @CheckedTemplate
    private static class Templates {
        public static native MailTemplate.MailTemplateInstance jobSummary(List<JobSummary> jobs,
                                                                          long totalMinutes,
                                                                          long totalSeconds,
                                                                          String documentUrl,
                                                                          UUID docId);

        public static native MailTemplate.MailTemplateInstance reprocessComplete(String documentUrl,
                                                                                 UUID docId);

        public static native MailTemplate.MailTemplateInstance failedDocProcessing(List<JobSummary> jobs,
                                                                                   long totalMinutes,
                                                                                   long totalSeconds,
                                                                                   String documentUrl,
                                                                                   UUID docId);
    }
}
