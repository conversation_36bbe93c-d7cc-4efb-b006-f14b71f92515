package com.walnut.vegaspread.workflow.service;

import com.walnut.vegaspread.common.model.audit.workflow.DocumentAuditDto;
import com.walnut.vegaspread.common.model.workflow.StatusEnum;
import com.walnut.vegaspread.common.utils.ConfigKeys;
import com.walnut.vegaspread.workflow.cloud.CloudProvider;
import com.walnut.vegaspread.workflow.cloud.CloudProviderFactory;
import com.walnut.vegaspread.workflow.entity.Document;
import com.walnut.vegaspread.workflow.entity.JobExecutionMonitoring;
import com.walnut.vegaspread.workflow.model.ApiKeyCredentialsDto;
import com.walnut.vegaspread.workflow.model.DocOutputDto;
import com.walnut.vegaspread.workflow.model.StageEnum;
import com.walnut.vegaspread.workflow.model.UpdateDocDto;
import com.walnut.vegaspread.workflow.model.WiseCallbackDto;
import com.walnut.vegaspread.workflow.utils.Config;
import com.walnut.vegaspread.workflow.utils.Utils;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.ws.rs.core.Response;
import org.apache.commons.lang3.StringUtils;
import org.eclipse.microprofile.config.inject.ConfigProperty;
import org.jboss.logging.Logger;

import java.net.URI;
import java.util.List;
import java.util.Objects;
import java.util.UUID;

import static com.walnut.vegaspread.workflow.service.WiseWorkflowService.JOB_MONITORING_ENDPOINT;
import static com.walnut.vegaspread.workflow.utils.Config.WALNUT_AI_USER;

@ApplicationScoped
public class ProcessorService {
    private static final Logger logger = Logger.getLogger(ProcessorService.class);
    private final DocumentService documentService;
    private final ExchangeService exchangeService;
    private final CloudProvider cloudProvider;
    private final MailingService mailingService;
    private final JobExecutionMonitoringService jobExecutionMonitoringService;
    @ConfigProperty(name = ConfigKeys.ENV_NAME_KEY)
    String envName;
    @ConfigProperty(name = Config.WISE_CLIENT_SECRET_KEY)
    String clientSecret;
    @ConfigProperty(name = Config.API_CLIENT_NAME)
    String clientName;
    @ConfigProperty(name = Config.FRONTEND_HOST_URL)
    String frontendHostUrl;

    public ProcessorService(DocumentService documentService, ExchangeService exchangeService,
                            CloudProviderFactory cloudProviderFactory,
                            MailingService mailingService,
                            JobExecutionMonitoringService jobExecutionMonitoringService) {
        this.documentService = documentService;
        this.exchangeService = exchangeService;
        this.cloudProvider = cloudProviderFactory.getCloudProvider();

        this.mailingService = mailingService;
        this.jobExecutionMonitoringService = jobExecutionMonitoringService;
    }

    public Response.Status startProcess(UUID docId, StageEnum processStage, URI baseUri) {
        DocOutputDto doc;
        if (processStage.equals(StageEnum.LEARNING)) {
            doc = documentService.docToOutput(documentService.getFromDb(docId));
        } else {
            doc = documentService.updateDocStatus(docId, StatusEnum.PROCESSING, processStage.toString());
        }
        if (doc == null) {
            return Response.Status.NOT_FOUND;
        }

        StageEnum cbStage = processStage.getNextStage();
        WiseWorkflowService wiseWorkflowService = getWiseWorkflowService(baseUri, doc, cbStage, processStage);
        wiseWorkflowService.startProcess(processStage);
        exchangeService.createDocAudit(
                List.of(new DocumentAuditDto.Create(docId, "PROCESSING", processStage.toString())));
        return Response.Status.OK;
    }

    public Response.Status processBlock(UUID docId, URI baseUri, Integer blockId) throws InterruptedException {
        DocOutputDto doc = documentService.docToOutput(documentService.getFromDb(docId));
        StageEnum processStage = StageEnum.REPROCESS_NTA_BLOCK;
        StageEnum cbStage = processStage.getNextStage();
        WiseWorkflowService wiseWorkflowService = getWiseWorkflowService(baseUri, doc, cbStage, processStage);
        wiseWorkflowService.startReprocessBlock(blockId);
        exchangeService.createDocAudit(
                List.of(new DocumentAuditDto.Create(docId, "PROCESSING_BLOCK", blockId.toString())));
        return Response.Status.OK;
    }

    private WiseWorkflowService getWiseWorkflowService(URI baseUri, DocOutputDto doc, StageEnum cbStage,
                                                       StageEnum errorStage) {
        String bucketName = cloudProvider.getBucketName();

        String startJobMonitoringCallbackUrl = baseUri.toString() + JOB_MONITORING_ENDPOINT + "/" + "start-job";
        logger.debugf("Starting job monitoring callback url: %s", startJobMonitoringCallbackUrl);

        String endJobMonitoringCallbackUrl = baseUri + JOB_MONITORING_ENDPOINT + "/" + "end-job";
        logger.debugf("End job monitoring callback url: %s", endJobMonitoringCallbackUrl);

        WiseCallbackDto.CbMetadata cbMetadata = new WiseCallbackDto.CbMetadata(doc.docId(),
                doc.createdTime().toLocalDate(), bucketName, Objects.equals(doc.documentType(), "Digital"), cbStage, 0,
                StringUtils.EMPTY, errorStage);

        ApiKeyCredentialsDto credentialsDto = new ApiKeyCredentialsDto(this.clientName, this.clientSecret);
        return new WiseWorkflowService(cbMetadata, credentialsDto, baseUri, cloudProvider, this.envName, doc);
    }

    public void handleFinishProcess(UUID docId) {

        List<JobExecutionMonitoring> jobs = jobExecutionMonitoringService.findByDocId(docId);
        Document doc = documentService.getFromDb(docId);

        String createdBy = doc.getAuditable().getCreatedBy();
        documentService.updateInDbByProcessor(docId, new UpdateDocDto(null, null, null, StatusEnum.UNDER_REVIEW_LVL_1),
                WALNUT_AI_USER);
        String email = exchangeService.getEmailForUser(createdBy);
        if (StringUtils.isEmpty(email)) {
            logger.errorf("Failed to send job summary email for docId: %s, no email found for user: %s",
                    docId, createdBy);
        } else {
            logger.debugf("Sending job summary email for docId: %s to %s with jobs %s", docId, email, jobs);
            mailingService.sendJobSummaryEmail(jobs, email, getDocUrl(doc), doc);
        }
    }

    public void handleFinishReprocess(UUID docId) {

        Document doc = documentService.getFromDb(docId);
        documentService.updateInDbByProcessor(docId, new UpdateDocDto(null, null, null, StatusEnum.UNDER_REVIEW_LVL_1),
                WALNUT_AI_USER);
        String currentReviewer = Utils.getCurrentReviewer(doc);
        if (StringUtils.isEmpty(currentReviewer)) {
            logger.errorf("Failed to send reprocess complete email for docId: %s, no current reviewer found",
                    docId);
            return;
        }
        String email = exchangeService.getEmailForUser(currentReviewer);
        if (StringUtils.isEmpty(email)) {
            logger.errorf("Failed to send reprocess complete email for docId: %s, no email found for user: %s",
                    docId, currentReviewer);
        } else {
            logger.debugf("Sending reprocess complete email for docId: %s to %s", docId, email);
            mailingService.sendReprocessCompleteEmail(doc, email, getDocUrl(doc));
        }
    }

    public String getDocUrl(Document doc) {
        return frontendHostUrl + "/review/" + doc.getSpreadingTask().getSpreadId() + "/doc/" + doc.getDocId();
    }

    public void handleFailedDocProcessing(UUID docId) {
        List<JobExecutionMonitoring> jobs = jobExecutionMonitoringService.findByDocId(docId);
        Document doc = documentService.getFromDb(docId);

        String createdBy = doc.getAuditable().getCreatedBy();
        String email = exchangeService.getEmailForUser(createdBy);
        if (StringUtils.isEmpty(email)) {
            logger.errorf("Failed to send job summary email for docId: %s, no email found for user: %s",
                    docId, createdBy);
        } else {
            logger.debugf("Sending failed doc processing email for docId: %s to %s", docId, email);
            mailingService.sendFailedDocProcessingEmail(jobs, email, getDocUrl(doc), doc);
        }
    }
}
