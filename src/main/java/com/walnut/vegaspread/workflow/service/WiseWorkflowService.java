package com.walnut.vegaspread.workflow.service;

import com.walnut.vegaspread.workflow.cloud.CloudProvider;
import com.walnut.vegaspread.workflow.model.ApiKeyCredentialsDto;
import com.walnut.vegaspread.workflow.model.DocOutputDto;
import com.walnut.vegaspread.workflow.model.StageEnum;
import com.walnut.vegaspread.workflow.model.WiseCallbackDto;
import com.walnut.vegaspread.workflow.model.WiseTaskNameDto;
import com.walnut.vegaspread.workflow.utils.Config;
import org.apache.commons.lang3.StringUtils;
import org.eclipse.microprofile.config.ConfigProvider;
import org.jboss.logging.Logger;

import java.net.URI;
import java.util.Collections;
import java.util.List;
import java.util.Map;

public class WiseWorkflowService {
    public static final String JOB_MONITORING_ENDPOINT = "wise/processor/job-execution-monitoring";
    private static final Logger logger = Logger.getLogger(WiseWorkflowService.class);
    private static final String CALLBACK_ENDPOINT = "callback";
    private final WiseCallbackDto.CbMetadata cbMetadata;
    private final ApiKeyCredentialsDto credentialsDto;
    private final URI baseUri;
    private final CloudProvider cloudProvider;
    private final String envName;
    private final DocOutputDto docOutputDto;
    private final String fullEnvName;

    public WiseWorkflowService(WiseCallbackDto.CbMetadata cbMetadata,
                               ApiKeyCredentialsDto credentialsDto,
                               URI baseUri,
                               CloudProvider cloudProvider,
                               String envName,
                               DocOutputDto docOutputDto) {
        this.cbMetadata = cbMetadata;
        this.credentialsDto = credentialsDto;
        this.baseUri = baseUri;
        this.cloudProvider = cloudProvider;
        this.fullEnvName = envName;
        this.envName = envName.split("-")[0];
        this.docOutputDto = docOutputDto;
    }

    private WiseCallbackDto.CallbackData getCallbackData() {
        String callbackUrl = baseUri.toString() + CALLBACK_ENDPOINT + "/" + this.cbMetadata.getDocId();

        String startJobMonitoringCallbackUrl = baseUri + JOB_MONITORING_ENDPOINT + "/" + "start-job";
        logger.debugf("Starting job monitoring callback url: %s", startJobMonitoringCallbackUrl);

        String endJobMonitoringCallbackUrl = baseUri + JOB_MONITORING_ENDPOINT + "/" + "end-job";
        logger.debugf("End job monitoring callback url: %s", endJobMonitoringCallbackUrl);

        logger.debugf("Callback URL: " + callbackUrl);
        return new WiseCallbackDto.CallbackData(callbackUrl, this.cbMetadata, WiseCallbackDto.CbMethod.POST,
                credentialsDto.client(), startJobMonitoringCallbackUrl, endJobMonitoringCallbackUrl);
    }

    private void getExecutionStatus(String jobName, String executionId) throws InterruptedException {
        cloudProvider.getExecutionStatus(jobName, executionId);
    }

    private String startJob(List<String> args, String jobName, String awsTaskFamily,
                            Map<String, String> envVars) {
        return cloudProvider.startJob(args, jobName, awsTaskFamily, envVars);
    }

    private void startRotationService() {

        WiseCallbackDto.OcrOrRotationDto rotationDto = new WiseCallbackDto.OcrOrRotationDto(
                this.cbMetadata, getCallbackData());
        String rotationTaskFamilyName = ConfigProvider.getConfig()
                .getOptionalValue(Config.ROTATION_TASK_FAMILY_NAME, String.class)
                .orElse("");
        String jobName = String.format(cloudProvider.getJobName(WiseTaskNameDto.ROTATION), this.envName);
        String taskFamily = String.format(rotationTaskFamilyName, this.envName);
        Map<String, String> envVars = cloudProvider.getEnvVars(WiseTaskNameDto.ROTATION);
        String executionId = startJob(rotationDto.generateArgs(), jobName,
                taskFamily, envVars);

        logger.infof("Started rotation service for docId: %s, execution id: %s",
                this.cbMetadata.getDocId(), executionId);
    }

    private void startOcrService() {
        WiseCallbackDto.OcrOrRotationDto ocrDto = new WiseCallbackDto.OcrOrRotationDto(
                this.cbMetadata, getCallbackData());

        String ocrTaskFamilyName = ConfigProvider.getConfig()
                .getOptionalValue(Config.OCR_TASK_FAMILY_NAME, String.class).orElse("");
        String jobName = String.format(cloudProvider.getJobName(WiseTaskNameDto.OCR), this.envName);
        String taskFamily = String.format(ocrTaskFamilyName, this.envName);
        Map<String, String> envVars = cloudProvider.getEnvVars(WiseTaskNameDto.OCR);
        String executionId = startJob(ocrDto.generateArgs(), jobName, taskFamily,
                envVars);

        logger.infof("Started OCR service for docId: %s, execution id: %s",
                this.cbMetadata.getDocId(), executionId);
    }

    private void startDocAi() {

        WiseCallbackDto.DocAiDto docAiDto = new WiseCallbackDto.DocAiDto(this.cbMetadata, Boolean.TRUE, Boolean.TRUE,
                getCallbackData(), this.credentialsDto.apiKey());
        cloudProvider.startSpecializedService(docAiDto);
    }

    private void startFsClf() {
        WiseCallbackDto.FsClfDto fsClfDto = new WiseCallbackDto.FsClfDto(this.cbMetadata, Boolean.TRUE,
                getCallbackData());
        String fsClfTaskFamilyName = ConfigProvider.getConfig()
                .getOptionalValue(Config.FS_CLF_TASK_FAMILY_NAME, String.class).orElse("");
        String jobName = String.format(cloudProvider.getJobName(WiseTaskNameDto.FS_CLF), this.envName);
        String taskFamily = String.format(fsClfTaskFamilyName, this.envName);
        Map<String, String> envVars = cloudProvider.getEnvVars(WiseTaskNameDto.FS_CLF);
        String executionId = startJob(fsClfDto.generateArgs(), jobName,
                taskFamily, envVars);

        logger.infof("Started FS Classifier service for docId: %s, execution id: %s",
                this.cbMetadata.getDocId(), executionId);
    }

    private void startProcessorService(WiseTaskNameDto taskName, StageEnum.ReprocessStage reprocessStage) {
        WiseCallbackDto.ProcessorDto processorDto = new WiseCallbackDto.ProcessorDto(this.cbMetadata.getDocId(),
                this.credentialsDto.client(), this.docOutputDto.clientName(), reprocessStage, getCallbackData());

        String taskFamily;
        Map<String, String> envVars;

        if ((WiseTaskNameDto.COMPLETE).equals(taskName)) {
            String completeTaskFamilyName = ConfigProvider.getConfig()
                    .getOptionalValue(Config.COMPLETE_TASK_FAMILY_NAME, String.class).orElse("");
            taskFamily = String.format(completeTaskFamilyName, this.fullEnvName);
            envVars = cloudProvider.getEnvVars(WiseTaskNameDto.COMPLETE);
        } else {
            String processorTaskFamilyName = ConfigProvider.getConfig()
                    .getOptionalValue(Config.PROCESSOR_TASK_FAMILY_NAME, String.class).orElse("");
            taskFamily = String.format(processorTaskFamilyName, this.envName);
            envVars = cloudProvider.getEnvVars(WiseTaskNameDto.PROCESSING);
        }
        String jobName = String.format(cloudProvider.getJobName(taskName), this.fullEnvName);
        String executionId = startJob(processorDto.generateArgs(), jobName,
                taskFamily, envVars);

        logger.infof("Started %s service for docId: %s, execution id: %s",
                jobName, this.cbMetadata.getDocId(), executionId);
    }

    public void handleCallback(StageEnum processStage) {
        switch (processStage) {
            case ROTATION -> startRotationService();
            case OCR -> startOcrService();
            case DOC_AI -> startDocAi();
            case FS_CLF -> startFsClf();
            case PROCESS -> startProcessorService(WiseTaskNameDto.PROCESSING,
                    processStage.getReprocessStage());
            default -> logger.errorf("Invalid callback process stage %s", processStage);
        }
    }

    public void startProcess(StageEnum processStage) {
        logger.infof("Starting process: %s", processStage);
        switch (processStage) {
            case ROTATION -> startRotationService();
            case OCR -> startOcrService();
            case DOC_AI -> startDocAi();
            case FS_CLF -> startFsClf();
            case PROCESS, REPROCESS_COMPLETE, REPROCESS_TABLE_TAG, REPROCESS_COA ->
                    startProcessorService(WiseTaskNameDto.PROCESSING, processStage.getReprocessStage());
            case LEARNING -> startProcessorService(WiseTaskNameDto.COMPLETE, processStage.getReprocessStage());
            default -> throw new IllegalStateException("Unexpected value: " + processStage);
        }
    }

    public void startReprocessBlock(Integer blockId) throws InterruptedException {
        logger.infof("Starting reprocess block %s", blockId);

        WiseCallbackDto.ProcessorBlockDto blockDto = new WiseCallbackDto.ProcessorBlockDto(
                this.cbMetadata.getDocId(),
                this.credentialsDto.client(),
                this.docOutputDto.clientName(),
                StageEnum.REPROCESS_NTA_BLOCK.getReprocessStage(),
                blockId);

        String jobName = String.format(cloudProvider.getJobName(WiseTaskNameDto.PROCESSING), this.fullEnvName);
        logger.infof("Starting reprocess block %s with args %s", blockId, blockDto.generateArgs());
        String executionId = startJob(blockDto.generateArgs(), jobName, StringUtils.EMPTY,
                Collections.emptyMap());

        getExecutionStatus(jobName, executionId);
    }
}
