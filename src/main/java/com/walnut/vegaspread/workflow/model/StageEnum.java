package com.walnut.vegaspread.workflow.model;

import org.jboss.logging.Logger;

public enum StageEnum {
    ROTATION,
    OCR,
    DOC_AI,
    FS_CLF,
    PROCESS(ReprocessStage.COMPLETE),
    LEARNING(ReprocessStage.LEARNING),
    REPROCESS_COMPLETE(ReprocessStage.COMPLETE),
    REPROCESS_TABLE_TAG(ReprocessStage.TABLE_TAG),
    REPROCESS_COA(ReprocessStage.COA),
    REPROCESS_NTA_BLOCK(ReprocessStage.NTA_BLOCK),
    //Stages to denote completion of a processing stage and end of callback.
    FINISH_PROCESS,
    FINISH_REPROCESS,
    FINISH_LEARNING;

    private static final Logger logger = Logger.getLogger(StageEnum.class);
    private final ReprocessStage reprocessStage;

    StageEnum() {
        this.reprocessStage = null;
    }

    StageEnum(ReprocessStage reprocessStage) {
        this.reprocessStage = reprocessStage;
    }

    public boolean isReprocess() {
        return reprocessStage != null;
    }

    public ReprocessStage getReprocessStage() {
        return reprocessStage;
    }

    public StageEnum getNextStage() {
        return switch (this) {
            case ROTATION -> StageEnum.OCR;
            case OCR -> StageEnum.DOC_AI;
            case DOC_AI -> StageEnum.FS_CLF;
            case FS_CLF -> StageEnum.PROCESS;
            case PROCESS -> StageEnum.FINISH_PROCESS;
            case LEARNING -> StageEnum.FINISH_LEARNING;
            case REPROCESS_COA, REPROCESS_COMPLETE, REPROCESS_TABLE_TAG, REPROCESS_NTA_BLOCK ->
                    StageEnum.FINISH_REPROCESS;
            default -> {
                logger.errorf("Invalid process stage for next step: %s", this);
                throw new IllegalArgumentException("Invalid process stage for next step: " + this);
            }
        };
    }

    public enum ReprocessStage {
        COMPLETE,
        TABLE_TAG,
        COA,
        NTA_BLOCK,
        LEARNING
    }
}
