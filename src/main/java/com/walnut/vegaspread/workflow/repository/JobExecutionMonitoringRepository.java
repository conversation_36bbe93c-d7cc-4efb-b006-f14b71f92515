package com.walnut.vegaspread.workflow.repository;

import com.walnut.vegaspread.workflow.entity.JobExecutionMonitoring;
import io.quarkus.hibernate.orm.panache.PanacheRepositoryBase;
import jakarta.enterprise.context.ApplicationScoped;

import java.util.List;
import java.util.UUID;

@ApplicationScoped
public class JobExecutionMonitoringRepository implements PanacheRepositoryBase<JobExecutionMonitoring, String> {

    public List<JobExecutionMonitoring> findByDocId(UUID docId) {
        return find("docId = ?1", docId).list();
    }
}
