package com.walnut.vegaspread.workflow.entity;

import com.walnut.vegaspread.workflow.model.JobExecutionDto;
import com.walnut.vegaspread.workflow.model.StageEnum;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

@Getter
@Setter
@Entity
@Builder
@Table(name = JobExecutionMonitoring.TABLE_NAME)
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class JobExecutionMonitoring {
    public static final String TABLE_NAME = "job_execution_monitoring";
    public static final String JOB_ID_COL_NAME = "job_id";
    public static final String DOC_ID_COL_NAME = "doc_id";
    public static final String STAGE_COL_NAME = "stage";
    public static final String START_TIME_COL_NAME = "start_time";
    public static final String END_TIME_COL_NAME = "end_time";
    public static final String IS_SUCCESS_COL_NAME = "is_success";

    @Id
    @Size(max = 255)
    @Column(name = JOB_ID_COL_NAME, nullable = false)
    public String jobId;

    @NotNull
    @Column(name = DOC_ID_COL_NAME, nullable = false)
    public UUID docId;

    @Enumerated(EnumType.STRING)
    @Column(name = STAGE_COL_NAME, nullable = false)
    public StageEnum stage;

    @NotNull
    @Column(name = START_TIME_COL_NAME, nullable = false)
    public LocalDateTime startTime;

    @Column(name = END_TIME_COL_NAME)
    public LocalDateTime endTime;

    @Column(name = IS_SUCCESS_COL_NAME)
    public Boolean isSuccess;

    public static List<JobExecutionDto.Response> toDtoList(List<JobExecutionMonitoring> jobExecutionMonitorings) {
        return jobExecutionMonitorings.stream()
                .map(JobExecutionMonitoring::toDto)
                .toList();
    }

    public static JobExecutionDto.Response toDto(JobExecutionMonitoring jobExecutionMonitoring) {
        return new JobExecutionDto.Response(
                jobExecutionMonitoring.getJobId(),
                jobExecutionMonitoring.getDocId(),
                jobExecutionMonitoring.getStage(),
                jobExecutionMonitoring.getStartTime(),
                jobExecutionMonitoring.getEndTime(),
                jobExecutionMonitoring.getIsSuccess()
        );
    }
}
