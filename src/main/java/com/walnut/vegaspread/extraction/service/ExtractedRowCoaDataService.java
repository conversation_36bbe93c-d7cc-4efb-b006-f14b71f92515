package com.walnut.vegaspread.extraction.service;

import com.walnut.vegaspread.common.exceptions.DeletionFailedException;
import com.walnut.vegaspread.common.model.audit.extraction.ExtractedRowCoaDataAuditDto;
import com.walnut.vegaspread.extraction.entity.CoaDataEntity;
import com.walnut.vegaspread.extraction.entity.CoaMappingEntity;
import com.walnut.vegaspread.extraction.entity.ExtractedTableRowCoaDataJoinEntity;
import com.walnut.vegaspread.extraction.entity.TableRowEntity;
import com.walnut.vegaspread.extraction.model.CoaDataDto;
import com.walnut.vegaspread.extraction.model.ExtractedTableRowCoaDataJoinDto;
import com.walnut.vegaspread.extraction.primarykey.ExtractedTableRowCoaDataPkId;
import com.walnut.vegaspread.extraction.primarykey.TableRowPkId;
import com.walnut.vegaspread.extraction.repository.ExtractedRowCoaDataRepository;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.transaction.Transactional;

import java.security.InvalidParameterException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

@ApplicationScoped
public class ExtractedRowCoaDataService {
    private final ExtractedRowCoaDataRepository extractedRowCoaDataRepository;
    private final CoaDataService coaDataService;
    private final CoaMappingService coaMappingService;
    private final ExchangeService exchangeService;

    public ExtractedRowCoaDataService(ExtractedRowCoaDataRepository extractedRowCoaDataRepository,
                                      CoaDataService coaDataService, CoaMappingService coaMappingService,
                                      ExchangeService exchangeService) {
        this.extractedRowCoaDataRepository = extractedRowCoaDataRepository;
        this.coaDataService = coaDataService;
        this.coaMappingService = coaMappingService;
        this.exchangeService = exchangeService;
    }

    @Transactional
    public List<ExtractedTableRowCoaDataJoinEntity> createOrUpdateCoaDataMapping(
            List<ExtractedTableRowCoaDataJoinDto.CreateOrUpdate> rowCoaJoinDtos,
            Boolean isApiKeyAuthenticated) {
        if (rowCoaJoinDtos == null || rowCoaJoinDtos.isEmpty()) {
            return Collections.emptyList();
        }
        List<ExtractedTableRowCoaDataJoinEntity> rowCoaJoinEntities = new ArrayList<>();

        Map<ExtractedTableRowCoaDataJoinDto.CreateOrUpdate, ExtractedTableRowCoaDataJoinEntity> rowCoaUpdateDtos =
                new HashMap<>();
        List<ExtractedTableRowCoaDataJoinDto.CreateOrUpdate> rowCoaCreateDtos = new ArrayList<>();
        List<ExtractedTableRowCoaDataJoinDto.CreateOrUpdate> rowCoaDeleteDtos = new ArrayList<>();

        rowCoaJoinDtos.forEach(rowCoaJoinDto -> {
            //Check for existing coa data.
            Optional<ExtractedTableRowCoaDataJoinEntity> optExistingRowCoaJoinEntity =
                    extractedRowCoaDataRepository.findByRowOptional(
                            rowCoaJoinDto.tableId(),
                            rowCoaJoinDto.rowId().shortValue());

            //Check if dto is coa data and coa id is not 1.
            if (rowCoaJoinDto.coaData() != null && rowCoaJoinDto.coaData().coaId() > 1) {
                //If coa data exists for row, update.
                if (optExistingRowCoaJoinEntity.isPresent()) {
                    //Update coa data
                    rowCoaUpdateDtos.put(rowCoaJoinDto, optExistingRowCoaJoinEntity.get());
                }
                // If coa data does not exist, create.
                else {
                    rowCoaCreateDtos.add(rowCoaJoinDto);
                }
            }
            //If dto has coa data and coa id is 1, delete.
            else if (rowCoaJoinDto.coaData() != null && rowCoaJoinDto.coaData().coaId() == 1) {
                rowCoaDeleteDtos.add(rowCoaJoinDto);
            }
        });
        deleteRowCoaDataMappings(rowCoaDeleteDtos, isApiKeyAuthenticated, false);
        rowCoaJoinEntities.addAll(createCoaDataMapping(rowCoaCreateDtos, isApiKeyAuthenticated));
        rowCoaJoinEntities.addAll(updateCoaDataMapping(rowCoaUpdateDtos, isApiKeyAuthenticated));

        return rowCoaJoinEntities;
    }

    @Transactional
    public List<ExtractedTableRowCoaDataJoinEntity> createCoaDataMapping(
            List<ExtractedTableRowCoaDataJoinDto.CreateOrUpdate> rowCoaCreateDtos,
            Boolean isApiKeyAuthenticated) {

        List<ExtractedTableRowCoaDataJoinEntity> rowCoaCreateEntities = new ArrayList<>();
        List<ExtractedRowCoaDataAuditDto.Create> rowCoaCreateAuditDtos = new ArrayList<>();

        for (ExtractedTableRowCoaDataJoinDto.CreateOrUpdate rowCoaCreateDto : rowCoaCreateDtos) {
            CoaMappingEntity coaMappingEntity;

            //Evaluate coa mapping for the row.

            //Set coa mapping to null for user created coa for row.
            if (Boolean.FALSE.equals(isApiKeyAuthenticated)) {
                coaMappingEntity = null;
            }
            //Processor created coa for row.
            else {

                Optional<CoaMappingEntity> optCoaMappingEntity;
                Integer explainabilityId = rowCoaCreateDto.explainabilityId();

                //If processor sends a non-null explainability id, get coa mapping for id.
                if (explainabilityId != null) {
                    optCoaMappingEntity = coaMappingService.findByIdOptional(explainabilityId);

                    if (optCoaMappingEntity.isEmpty()) {
                        throw new InvalidParameterException(
                                "Invalid coa mapping id " + rowCoaCreateDto.explainabilityId());
                    }
                    coaMappingEntity = optCoaMappingEntity.get();
                 /* We are creating a new join mapping, so audit only non-null explainability (to avoid null -> null
                     audit).*/
                    rowCoaCreateAuditDtos.add(
                            new ExtractedRowCoaDataAuditDto.Create(rowCoaCreateDto.tableId(), rowCoaCreateDto.rowId(),
                                    ExtractedTableRowCoaDataJoinEntity.EXPLAINABILITY_ID_COL_NAME,
                                    coaMappingEntity.getId().toString()));
                }
                //If processor sends a null explainability id, set coa mapping to null.
                else {
                    coaMappingEntity = null;
                }
            }

            //Evaluate coa data for the row.

            CoaDataDto.Create coaData = rowCoaCreateDto.coaData();
            Integer tableId = rowCoaCreateDto.tableId();
            Integer rowId = rowCoaCreateDto.rowId();

           /* Find the coa data entity for the given useCoa,coaId and coaScore.If missing, add coa data and get the
             entity.*/
            CoaDataEntity joinCoaData = coaDataService.findByUseCoaAndCoaIdAndCoaScore(
                            coaData.useCoa(), coaData.coaId(), coaData.coaScore())
                    .orElseGet(() -> coaDataService.addCoaData(coaData));

            // Add the join mapping using the evaluated coa mapping and coa data.
            rowCoaCreateEntities.add(new ExtractedTableRowCoaDataJoinEntity(new TableRowPkId(tableId, rowId),
                    joinCoaData.getId(), coaMappingEntity));

            rowCoaCreateAuditDtos.add(new ExtractedRowCoaDataAuditDto.Create(
                            rowCoaCreateDto.tableId(), rowCoaCreateDto.rowId(), TableRowEntity.COA_ID_COL_NAME,
                            rowCoaCreateDto.coaData().coaId().toString()
                    )
            );
        }
        extractedRowCoaDataRepository.saveRowCoaJoin(rowCoaCreateEntities);
        exchangeService.auditExtractedRowCoaDataCreate(rowCoaCreateAuditDtos, isApiKeyAuthenticated);

        return rowCoaCreateEntities;
    }

    @Transactional
    public List<ExtractedTableRowCoaDataJoinEntity> updateCoaDataMapping(
            Map<ExtractedTableRowCoaDataJoinDto.CreateOrUpdate, ExtractedTableRowCoaDataJoinEntity> rowCoaJoinUpdateDtos,
            Boolean isApiKeyAuthenticated) {

        List<ExtractedTableRowCoaDataJoinEntity> rowCoaUpdateEntities = new ArrayList<>();
        List<ExtractedRowCoaDataAuditDto.Update> rowCoaUpdateAuditDtos = new ArrayList<>();

        rowCoaJoinUpdateDtos.forEach((rowCoaJoinUpdateDto, rowCoaJoinUpdateEntity) -> {
            Integer tableId = rowCoaJoinUpdateDto.tableId();
            Integer rowId = rowCoaJoinUpdateDto.rowId();
            ExtractedTableRowCoaDataPkId coaDataPkId = rowCoaJoinUpdateEntity.getExtractedTableRowCoaDataPkId();

            //Get the existing join mapping.
            ExtractedTableRowCoaDataJoinEntity currentMapping = new ExtractedTableRowCoaDataJoinEntity(
                    coaDataPkId.getTableRowPkId(), coaDataPkId.getCoaDataId(),
                    rowCoaJoinUpdateEntity.getExplainability());

            CoaDataDto.Create coaData = rowCoaJoinUpdateDto.coaData();

            //Check if coa data has been updated.(If one or more of useCoa,coaId or coaScore has changed).
            if (coaDataService.isUpdatedCoaData(currentMapping, coaData)) {

                Integer currentCoaId = getCoaIdFromCoaDataId(coaDataPkId.getCoaDataId());

                //Delete existing mapping to avoid concurrent update error.
                deleteRowCoaDataMapping(tableId, rowId, isApiKeyAuthenticated, true);

                //Get the coa data entity for the new information, or add if missing and get the new entity.
                CoaDataEntity coaDataMapping = coaDataService.findByUseCoaAndCoaIdAndCoaScore(coaData.useCoa(),
                                coaData.coaId(), coaData.coaScore())
                        .orElseGet(() -> coaDataService.addCoaData(coaData));

                Integer coaDataId = coaDataMapping.getId();
                //Set the new coa data.
                currentMapping.getExtractedTableRowCoaDataPkId().setCoaDataId(coaDataId);
                Integer updatedCoaId = getCoaIdFromCoaDataId(coaDataId);

                // If coa id has been updated audit it.
                if (!Objects.equals(updatedCoaId, currentCoaId)) {
                    rowCoaUpdateAuditDtos.add(
                            new ExtractedRowCoaDataAuditDto.Update(tableId, rowId, CoaDataEntity.COA_ID_COL_NAME,
                                    currentCoaId.toString(), updatedCoaId.toString()
                            )
                    );
                    //If coa id has been updated, update explainability in the mapping if needed.
                    updateExplainability(isApiKeyAuthenticated, rowCoaJoinUpdateDto, currentMapping,
                            rowCoaUpdateAuditDtos);
                }
            }
            rowCoaUpdateEntities.add(currentMapping);
        });
        extractedRowCoaDataRepository.saveRowCoaJoin(rowCoaUpdateEntities);
        exchangeService.auditExtractedRowCoaDataUpdate(rowCoaUpdateAuditDtos, isApiKeyAuthenticated);

        return rowCoaUpdateEntities;
    }

    private Integer getCoaIdFromCoaDataId(Integer coaDataId) {
        return coaDataService.findById(coaDataId).getCoaId();
    }

    public ExtractedTableRowCoaDataJoinEntity updateExplainability(Boolean isApiKeyAuthenticated,
                                                                   ExtractedTableRowCoaDataJoinDto.CreateOrUpdate rowCoaJoinUpdateDto,
                                                                   ExtractedTableRowCoaDataJoinEntity rowCoaJoinEntity,
                                                                   List<ExtractedRowCoaDataAuditDto.Update> rowCoaUpdateAuditDtos) {
        //Check if explainability has been updated.
        if (!Objects.equals(
                Optional.ofNullable(rowCoaJoinEntity.explainability).map(CoaMappingEntity::getId).orElse(null),
                rowCoaJoinUpdateDto.explainabilityId())
        ) {
            //If it is a user update, or if it is a processor update and new value is null. Audit and set value to null.
            if (Boolean.FALSE.equals(isApiKeyAuthenticated) || (Boolean.TRUE.equals(
                    isApiKeyAuthenticated) && rowCoaJoinUpdateDto.explainabilityId() == null)) {
                rowCoaUpdateAuditDtos.add(
                        new ExtractedRowCoaDataAuditDto.Update(
                                rowCoaJoinEntity.getExtractedTableRowCoaDataPkId().getTableRowPkId().getTableId(),
                                rowCoaJoinEntity.getExtractedTableRowCoaDataPkId()
                                        .getTableRowPkId()
                                        .getRowId()
                                        .intValue(),
                                ExtractedTableRowCoaDataJoinEntity.EXPLAINABILITY_ID_COL_NAME,
                                rowCoaJoinEntity.getExplainability().getId().toString(), null
                        )
                );
                rowCoaJoinEntity.setExplainability(null);
            }
            // If processor updated and explainability is non-null.
            else {
                //Find the coa mapping for the explainability id.
                Optional<CoaMappingEntity> optCoaMappingEntity = coaMappingService.findByIdOptional(
                        rowCoaJoinUpdateDto.explainabilityId());
                if (optCoaMappingEntity.isEmpty()) {
                    throw new InvalidParameterException(
                            "Invalid coa mapping id " + rowCoaJoinUpdateDto.explainabilityId());
                }
                //Audit and set the new coa mapping.
                else {
                    rowCoaUpdateAuditDtos.add(
                            new ExtractedRowCoaDataAuditDto.Update(
                                    rowCoaJoinEntity.getExtractedTableRowCoaDataPkId().getTableRowPkId().getTableId(),
                                    rowCoaJoinEntity.getExtractedTableRowCoaDataPkId()
                                            .getTableRowPkId()
                                            .getRowId()
                                            .intValue(),
                                    ExtractedTableRowCoaDataJoinEntity.EXPLAINABILITY_ID_COL_NAME,
                                    rowCoaJoinEntity.getExplainability() == null ? null :
                                            rowCoaJoinEntity.getExplainability()
                                                    .getId()
                                                    .toString(),
                                    optCoaMappingEntity.get().getId().toString()
                            )
                    );
                    rowCoaJoinEntity.setExplainability(optCoaMappingEntity.get());
                }
            }
        }
        return rowCoaJoinEntity;
    }

    public void deleteRowCoaDataMappings(List<ExtractedTableRowCoaDataJoinDto.CreateOrUpdate> rowCoaJoinDeleteDtos,
                                         Boolean isApiKeyAuthenticated, boolean isUpdate) {

        for (ExtractedTableRowCoaDataJoinDto.CreateOrUpdate rowCoaJoinDeleteDto : rowCoaJoinDeleteDtos) {
            deleteRowCoaDataMapping(rowCoaJoinDeleteDto.tableId(), rowCoaJoinDeleteDto.rowId(), isApiKeyAuthenticated,
                    isUpdate);
        }
    }

    @Transactional
    public void deleteRowCoaDataMapping(Integer tableId, Integer rowId, Boolean isApiKeyAuthenticated,
                                        boolean isUpdate) {
        List<ExtractedRowCoaDataAuditDto.Delete> rowCoaDeleteAuditDtos = new ArrayList<>();

        //Find the join mapping for deletion.
        Optional<ExtractedTableRowCoaDataJoinEntity> optRowCoaJoinEntity =
                extractedRowCoaDataRepository.findByRowOptional(
                        tableId, rowId.shortValue());
        if (optRowCoaJoinEntity.isEmpty()) {
            throw new InvalidParameterException(
                    "Row coa data mapping cannot be deleted due to missing row for table " + tableId + " row " + rowId);
        }
        // Audit deletion, if the join mapping is not being deleted as a part of update(since that is an update audit).
        else if (!isUpdate) {
            //Find the join entity.
            ExtractedTableRowCoaDataJoinEntity coaDataJoinEntity = optRowCoaJoinEntity.get();
            //Audit coa id deletion.
            rowCoaDeleteAuditDtos.add(
                    new ExtractedRowCoaDataAuditDto.Delete(tableId, rowId, CoaDataEntity.COA_ID_COL_NAME,
                            getCoaIdFromCoaDataId(
                                    coaDataJoinEntity.getExtractedTableRowCoaDataPkId().getCoaDataId()).toString()));
            // If existing explainability is non-null(To avoid null->null audit), audit deletion.
            if (coaDataJoinEntity.getExplainability() != null) {
                rowCoaDeleteAuditDtos.add(new ExtractedRowCoaDataAuditDto.Delete(tableId, rowId,
                        ExtractedTableRowCoaDataJoinEntity.EXPLAINABILITY_ID_COL_NAME,
                        coaDataJoinEntity.getExplainability()
                                .getId()
                                .toString()));
            }
        }

        //Delete the join mapping.
        long deleteCount = extractedRowCoaDataRepository.deleteMappedCoaData(tableId, rowId.shortValue());
        if (deleteCount != 1) {
            throw new DeletionFailedException(
                    "Failed to delete coa data mapping for row " + rowId + " in table " + tableId);
        }
        exchangeService.auditExtractedRowCoaDataDelete(rowCoaDeleteAuditDtos, isApiKeyAuthenticated);
    }

    @Transactional
    public void deleteTableCoaDataMappings(List<Integer> tableIds, Boolean isApiKeyAuthenticated) {
        // Find join mappings for the tables.
        List<ExtractedTableRowCoaDataJoinEntity> coaDataJoinEntitiesForTables =
                extractedRowCoaDataRepository.findByTableIds(tableIds);

        //Create audit entities for coa id deletion and explainability if non-nul(To avoid null->null audit).
        List<ExtractedRowCoaDataAuditDto.Delete> extractedRowCoaDataAuditDeleteDtos =
                coaDataJoinEntitiesForTables.stream()
                        .flatMap(extractedTableRowCoaDataJoinEntity -> {
                                    List<ExtractedRowCoaDataAuditDto.Delete> deleteAuditDtos = new ArrayList<>();
                                    deleteAuditDtos.add(new ExtractedRowCoaDataAuditDto.Delete(
                                            extractedTableRowCoaDataJoinEntity.getExtractedTableRowCoaDataPkId()
                                                    .getTableRowPkId().getTableId(),
                                            extractedTableRowCoaDataJoinEntity.getExtractedTableRowCoaDataPkId()
                                                    .getTableRowPkId().getRowId().intValue(),
                                            CoaDataEntity.COA_ID_COL_NAME,
                                            getCoaIdFromCoaDataId(
                                                    extractedTableRowCoaDataJoinEntity.getExtractedTableRowCoaDataPkId()
                                                            .getCoaDataId()).toString()
                                    ));
                                    if (extractedTableRowCoaDataJoinEntity.getExplainability() != null) {
                                        deleteAuditDtos.add(new ExtractedRowCoaDataAuditDto.Delete(
                                                extractedTableRowCoaDataJoinEntity.getExtractedTableRowCoaDataPkId()
                                                        .getTableRowPkId().getTableId(),
                                                extractedTableRowCoaDataJoinEntity.getExtractedTableRowCoaDataPkId()
                                                        .getTableRowPkId().getRowId().intValue(),
                                                ExtractedTableRowCoaDataJoinEntity.EXPLAINABILITY_ID_COL_NAME,
                                                extractedTableRowCoaDataJoinEntity.getExplainability().getId().toString()
                                        ));
                                    }
                                    return deleteAuditDtos.stream();
                                }
                        )
                        .toList();
        //Delete mappings.
        long deleteCount = extractedRowCoaDataRepository.deleteMappedCoaDataForTables(tableIds);
        if (deleteCount != coaDataJoinEntitiesForTables.size()) {
            throw new DeletionFailedException("Failed to delete one or more coa data mapping for tables " + tableIds);
        }
        exchangeService.auditExtractedRowCoaDataDelete(extractedRowCoaDataAuditDeleteDtos, isApiKeyAuthenticated);
    }

    public Optional<ExtractedTableRowCoaDataJoinEntity> findByRowOptional(
            Integer tableId, Integer rowId) {
        return extractedRowCoaDataRepository.findByRowOptional(tableId, rowId.shortValue());
    }

    public List<ExtractedTableRowCoaDataJoinEntity> findCoaDataMappingsForTable(List<Integer> tablesIds) {
        return extractedRowCoaDataRepository.findByTableIds(tablesIds);
    }

    public List<Integer> getTablesWithCoaMappingsInList(List<Integer> tableIds) {
        return extractedRowCoaDataRepository.findTableIdsWithCoaMappingsInList(tableIds);
    }
}
