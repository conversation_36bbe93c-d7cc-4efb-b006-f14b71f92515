package com.walnut.vegaspread.extraction.service;

import com.walnut.vegaspread.common.exceptions.DeletionFailedException;
import com.walnut.vegaspread.common.model.audit.extraction.LayoutBlockAuditDto;
import com.walnut.vegaspread.extraction.converter.LayoutBlockId;
import com.walnut.vegaspread.extraction.entity.Bbox;
import com.walnut.vegaspread.extraction.entity.LayoutBlockEntity;
import com.walnut.vegaspread.extraction.model.BlockTypeEnum;
import com.walnut.vegaspread.extraction.model.DbBlock;
import com.walnut.vegaspread.extraction.model.DbRow;
import com.walnut.vegaspread.extraction.repository.LayoutBlockRepository;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.transaction.Transactional;
import jakarta.validation.ConstraintViolationException;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;

@ApplicationScoped
public class LayoutService {

    public static final int NA_TAG_EXPLAINABILITY_ID = 0;
    private static final Logger log = LogManager.getLogger(LayoutService.class);
    private final LayoutBlockRepository layoutBlockRepository;
    private final ExchangeService exchangeService;
    private final TableService tableService;
    private final ExtractedRowCoaDataService extractedRowCoaDataService;

    public LayoutService(LayoutBlockRepository layoutBlockRepository,
                         ExchangeService exchangeService, TableService tableService,
                         ExtractedRowCoaDataService extractedRowCoaDataService) {
        this.layoutBlockRepository = layoutBlockRepository;
        this.exchangeService = exchangeService;
        this.tableService = tableService;
        this.extractedRowCoaDataService = extractedRowCoaDataService;
    }

    private String getBlockTag(String tag) {
        return tag == null || tag.equals("NA") ? StringUtils.EMPTY : tag;
    }

    public LayoutBlockEntity getBlock(Integer blockId) {
        return layoutBlockRepository.findById(blockId);
    }

    public List<LayoutBlockEntity> getBlocks(List<Integer> blockIds) {
        return layoutBlockRepository.findAllByIds(blockIds);
    }

    @Transactional
    public List<LayoutBlockEntity> createOrUpdateBlocks(List<DbBlock.BlockDto> blockDtos, boolean isUpdate,
                                                        boolean isApiKeyAuthenticated) {
        if (!isUpdate) {
            return createBlocks(blockDtos);
        }
        List<LayoutBlockEntity> blocks = new ArrayList<>();
        List<LayoutBlockAuditDto.Update> auditUpdateDtos = new ArrayList<>();
        Map<LayoutBlockEntity, List<BlockUpdateField>> blockUpdatedFields = new HashMap<>();
        for (DbBlock.BlockDto dto : blockDtos) {

            LayoutBlockEntity block = layoutBlockRepository.findById(dto.getBlockId());

            LayoutBlockEntity existingBlock = new LayoutBlockEntity(block, false);

            for (LayoutService.BlockUpdateField field : LayoutService.BlockUpdateField.values()) {

                if (updateField(field, block, existingBlock, dto, auditUpdateDtos)) {
                    blockUpdatedFields.computeIfAbsent(block, k -> new ArrayList<>()).add(field);
                }
            }

            if (isApiKeyAuthenticated && dto.getTagExplainabilityId() != null) {

                auditUpdateDtos.add(new LayoutBlockAuditDto.Update(block.getBlockId(),
                        LayoutBlockEntity.TAG_EXPLAINABILITY_ID_COL_NAME,
                        existingBlock.getTagExplainabilityId().toString(),
                        dto.getTagExplainabilityId().toString()));

                block.setTagExplainabilityId(dto.getTagExplainabilityId());
            }
            blocks.add(block);
        }
        layoutBlockRepository.persist(blocks);
        updateBlocksWithNATag(isApiKeyAuthenticated, blockUpdatedFields);
        if (!auditUpdateDtos.isEmpty()) {
            exchangeService.auditUpdateBlocks(auditUpdateDtos, isApiKeyAuthenticated);
        }

        return blocks;
    }

    private void updateBlocksWithNATag(boolean isApiKeyAuthenticated,
                                       Map<LayoutBlockEntity, List<BlockUpdateField>> blockUpdatedFields) {
        //Get blocks with NA table tag.
        List<LayoutBlockEntity> blockWithNATag = new ArrayList<>();
        blockUpdatedFields.forEach((block, updatedFields) -> {
            if (updatedFields.contains(BlockUpdateField.TAG) && block.getTag().isEmpty()) {
                blockWithNATag.add(block);
            }
        });

        //Remove NTA table mapping for all rows for all blocks with NA tag.
        List<DbRow.RowDto> updatedNtaRows = new ArrayList<>();
        blockWithNATag.forEach(block ->
                updatedNtaRows.addAll(
                        tableService.findByNtaTableId(block.getBlockId())
                                .stream()
                                .map(row -> DbRow.RowDto.builder()
                                        .tableId(row.getTableRowPkId().getTableId())
                                        .rowId(row.getTableRowPkId().getRowId())
                                        .ntaTableId(0)
                                        .build())
                                .toList())
        );
        if (!updatedNtaRows.isEmpty()) {
            tableService.createOrUpdateRows(updatedNtaRows, true, isApiKeyAuthenticated, false);
        }
        //Delete coa mappings for all blocks with NA tags.
        extractedRowCoaDataService.deleteTableCoaDataMappings(
                blockWithNATag.stream()
                        .map(LayoutBlockEntity::getBlockId)
                        .toList(),
                isApiKeyAuthenticated);
    }

    private void addAuditEntry(LayoutService.BlockUpdateField field, Integer blockId, Object currentValue,
                               Object newValue, List<LayoutBlockAuditDto.Update> auditChanges) {
        switch (field) {
            case BBOX:
                Bbox existingBbox = (Bbox) currentValue;
                Bbox dto = (Bbox) newValue;
                if (dto.getXMin() != null && dto.getYMin() != null && dto.getXMax() != null && dto.getYMax() != null) {
                    auditChanges.add(new LayoutBlockAuditDto.Update(blockId, Bbox.X_MIN_COL_NAME,
                            existingBbox.getXMin().toString(), dto.getXMin().toString()));
                    auditChanges.add(new LayoutBlockAuditDto.Update(blockId, Bbox.Y_MIN_COL_NAME,
                            existingBbox.getYMin().toString(), dto.getYMin().toString()));
                    auditChanges.add(new LayoutBlockAuditDto.Update(blockId, Bbox.X_MAX_COL_NAME,
                            existingBbox.getXMax().toString(), dto.getXMax().toString()));
                    auditChanges.add(new LayoutBlockAuditDto.Update(blockId, Bbox.Y_MAX_COL_NAME,
                            existingBbox.getYMax().toString(), dto.getYMax().toString()));
                }
                break;
            case TAG:
                auditChanges.add(new LayoutBlockAuditDto.Update(blockId, LayoutBlockEntity.TAG_COL_NAME,
                        currentValue.toString(), newValue.toString()));
                break;
            case COMMENT:
                auditChanges.add(new LayoutBlockAuditDto.Update(blockId, LayoutBlockEntity.COMMENT_COL_NAME,
                        currentValue.toString(), newValue.toString()));
                break;

            default:
                break;
        }
    }

    private boolean updateField(BlockUpdateField field, LayoutBlockEntity block, LayoutBlockEntity currentBlock,
                                DbBlock.BlockDto blockDto, List<LayoutBlockAuditDto.Update> auditChanges) {
        Object newValue = getNewValue(field, blockDto);
        Object currentValue = getCurrentValue(field, currentBlock);

        if (newValue != null && !newValue.equals(currentValue)) {
            updateValue(field, block, blockDto);
            addAuditEntry(field, block.getBlockId(), currentValue, newValue, auditChanges);
            return true;
        }
        return false;
    }

    private void updateValue(LayoutService.BlockUpdateField field, LayoutBlockEntity block, DbBlock.BlockDto blockDto) {
        switch (field) {
            case PAGE_NUM:
                block.setPageNum(blockDto.getPageNum().shortValue());
                break;
            case BLOCK_TYPE:
                block.setBlockType(blockDto.getBlockType());
                break;
            case BBOX:
                block.setBbox(new Bbox(blockDto.getXMin(), blockDto.getXMax(), blockDto.getYMin(), blockDto.getYMax()));
                break;
            case SCORE:
                block.setScore(blockDto.getScore().byteValue());
                break;
            case TAG:
                block.setTag(getBlockTag(blockDto.getTag()));
                break;
            case COMMENT:
                block.setComment(blockDto.getComment());
                break;
            default:
                break;
        }
    }

    private Object getCurrentValue(BlockUpdateField field, LayoutBlockEntity currentBlock) {
        return switch (field) {
            case PAGE_NUM -> currentBlock.getPageNum();
            case BLOCK_TYPE -> currentBlock.getBlockType();
            case BBOX -> currentBlock.getBbox();
            case SCORE -> currentBlock.getScore();
            case TAG -> Optional.ofNullable(currentBlock.getTag()).orElse(StringUtils.EMPTY);
            case COMMENT -> currentBlock.getComment();
        };
    }

    private Object getNewValue(BlockUpdateField field, DbBlock.BlockDto blockDto) {
        return switch (field) {
            case PAGE_NUM -> blockDto.getPageNum();
            case BLOCK_TYPE -> blockDto.getBlockType();
            case BBOX -> {
                if (blockDto.getXMin() != null && blockDto.getXMax() != null && blockDto.getYMin() != null && blockDto.getYMax() != null) {
                    yield new Bbox(blockDto.getXMin(), blockDto.getXMax(), blockDto.getYMin(), blockDto.getYMax());
                } else {
                    yield null;
                }
            }
            case SCORE -> blockDto.getScore() == null ? null : blockDto.getScore().byteValue();
            case TAG -> getBlockTag(blockDto.getTag());
            case COMMENT -> blockDto.getComment();
        };
    }

    private List<LayoutBlockEntity> createBlocks(List<DbBlock.BlockDto> blockDtos) {

        List<LayoutBlockEntity> blocks = new ArrayList<>();
        for (DbBlock.BlockDto dto : blockDtos) {

            LayoutBlockEntity block = LayoutBlockEntity.builder()
                    .docId(dto.getDocId())
                    .tagExplainabilityId(NA_TAG_EXPLAINABILITY_ID)
                    .pageNum(dto.getPageNum() == null ? null : dto.getPageNum().shortValue())
                    .blockType(dto.getBlockType())
                    .bbox(new Bbox(dto.getXMin(), dto.getXMax(), dto.getYMin(), dto.getYMax()))
                    .score(dto.getScore().byteValue())
                    .tag(getBlockTag(dto.getTag()))
                    .comment(Optional.ofNullable(dto.getComment()).orElse(StringUtils.EMPTY))
                    .build();
            blocks.add(block);
        }
        layoutBlockRepository.persist(blocks);
        return blocks;
    }

    @Transactional
    public void deleteBlockById(Integer blockId) {
        try {
            layoutBlockRepository.deleteById(blockId);
        } catch (ConstraintViolationException ex) {
            log.error(ex.getMessage());
        }
    }

    @Transactional
    public long deleteBlocksByIds(List<Integer> blockIds) {
        long deleteCount = 0;
        try {
            exchangeService.auditFirstAndLastBlocksForDelete(blockIds);
            log.debug("{} rows deleted for blocks {}", tableService.deleteRows(blockIds), blockIds);
            log.debug("{} headers deleted for blocks {}", tableService.deleteHeaders(blockIds), blockIds);
            deleteCount = layoutBlockRepository.deleteAllByBlockIds(blockIds);
            if (deleteCount != blockIds.size()) {
                throw new DeletionFailedException("All blocks with ids " + blockIds + " could not be deleted.");
            }
        } catch (ConstraintViolationException cve) {
            log.error(cve.getMessage());
        }
        return deleteCount;
    }

    @Transactional
    public long deleteBlocksByDocId(UUID docId) {
        long deleteCount = 0;
        try {
            deleteCount = deleteBlocksByIds(layoutBlockRepository.findAllBlockIdsByDocId(docId));
        } catch (ConstraintViolationException cve) {
            log.error(cve.getMessage());
        }
        return deleteCount;
    }

    public List<LayoutBlockEntity> listBlocksForDocId(UUID docId, BlockTypeEnum blockType) {
        return layoutBlockRepository.findAllByDocIdAndBlockType(docId, blockType);
    }

    public List<LayoutBlockEntity> listBlocksForDocIdAndPageNum(UUID docId, Integer pageNum, BlockTypeEnum blockType) {
        return layoutBlockRepository.findAllByDocIdAndBlockTypeAndPageNum(docId, blockType, pageNum);
    }

    public List<DbBlock.BlockTagOnly> getTags(UUID docId, BlockTypeEnum blockType) {
        return layoutBlockRepository.findBlocksWithTag(docId, blockType);
    }

    public List<Integer> findBlocksWithCoaId(UUID docId) {
        return layoutBlockRepository.findAllByDocIdWithCoaId(docId)
                .stream()
                .map(LayoutBlockId::getBlockId)
                .toList();
    }

    public List<LayoutBlockEntity> findAllBlocksInDocWithTag(UUID docId, String tag) {
        return layoutBlockRepository.findAllBlocksByDocIdAndTag(docId, tag);
    }

    public List<Integer> getBlockIdsForDoc(UUID docId) {
        return layoutBlockRepository.findAllBlockIdsByDocId(docId);
    }

    public List<LayoutBlockEntity> getTaggedTables(UUID docId) {
        return layoutBlockRepository.findAllTaggedTables(docId);
    }

    public enum BlockUpdateField {
        PAGE_NUM,
        BLOCK_TYPE,
        BBOX,
        SCORE,
        TAG,
        COMMENT
    }
}
