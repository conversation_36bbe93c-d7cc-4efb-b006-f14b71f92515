package com.walnut.vegaspread.audit.resource;

import com.walnut.vegaspread.common.utils.ConfigKeys;
import io.quarkus.security.Authenticated;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.core.Context;
import jakarta.ws.rs.core.UriInfo;
import org.eclipse.microprofile.config.inject.ConfigProperty;

import java.net.URI;

@Path("/health")
public class HealthResource {

    @ConfigProperty(name = ConfigKeys.APP_VERSION)
    String appVersion;

    @GET
    @Path("/ready")
    public String ready() {
        return "OK";
    }

    @GET
    @Path("/base-url")
    public URI baseUrl(@Context UriInfo uriInfo) {
        return uriInfo.getBaseUri();
    }

    @Authenticated
    @GET
    @Path("/authenticated")
    public String authenticated() {
        return "OK";
    }

    @GET
    @Path("/app-version")
    public String appVersion() {
        return appVersion;
    }
}
