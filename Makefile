BUILD_NUMBER=local

ENV=dev
SERVICE_NAME=coa
ENV_UPPER=$(shell echo ${ENV} | tr '[:lower:]' '[:upper:]')
GCLOUD_RUN_CPU=1
GCLOUD_RUN_MEM=512Mi
GCLOUD_RUN_CONCURRENCY=80
GCLOUD_RUN_MAX_INSTANCES=1
BUILD_DATE=$(shell date "+%d-%m-%y")
BUILD_ID=${ENV}-${BUILD_DATE}-${BUILD_NUMBER}
CLOUD_SQL_INSTANCE=vegaspread-7586a:asia-southeast1:vega-db
GCLOUD_RUN_MAX_INSTANCES=1
GCR_REPO=asia-southeast1-docker.pkg.dev/vegaspread-7586a/java

KEYCLOAK_REALM=dev-vega
KEYCLOAK_BASE_URL=https://auth.vegaspread.cloud
KEYCLOAK_REALM_URL=${KEY<PERSON>OAK_BASE_URL}/realms/${K<PERSON><PERSON><PERSON><PERSON><PERSON>_REALM}

AWS_ACCOUNT_ID = $(shell aws sts get-caller-identity --query Account --output text)
LAMBDA_S3_BUCKET_NAME = dev-vega-build-artifacts-${AWS_ACCOUNT_ID}

deploy:
	gcloud artifacts docker images delete ${GCR_REPO}/${ENV}-${SERVICE_NAME} --quiet || true
	docker tag thewalnutai/vega-${SERVICE_NAME}:${BUILD_ID} ${GCR_REPO}/${ENV}-${SERVICE_NAME}
	docker push ${GCR_REPO}/${ENV}-${SERVICE_NAME}

	gcloud run deploy ${ENV}-vega-${SERVICE_NAME} \
		--image ${GCR_REPO}/${ENV}-${SERVICE_NAME} \
		--allow-unauthenticated --port=8080 --cpu=${GCLOUD_RUN_CPU} --memory=${GCLOUD_RUN_MEM} \
		--max-instances=${GCLOUD_RUN_MAX_INSTANCES} --concurrency ${GCLOUD_RUN_CONCURRENCY} \
		--region=asia-southeast1 --cpu-boost --cpu-throttling \
		--set-env-vars=VEGA_ENV=${ENV} \
		--set-env-vars QUARKUS_OIDC_AUTH_SERVER_URL=${KEYCLOAK_REALM_URL} \
		--set-env-vars CLOUD_SQL_INSTANCE=${CLOUD_SQL_INSTANCE} \
		--project=vegaspread-7586a --service-account=${ENV}-<EMAIL>

deploy-native:
	QUARKUS_SMALLRYE_OPENAPI_OAUTH2_IMPLICIT_AUTHORIZATION_URL=https://auth.vegaspread.cloud/realms/dev-vega/protocol/openid-connect/auth mvn clean package -Pnative,gcp
	docker build -f src/main/docker/Dockerfile.native-micro -t thewalnutai/vega-${SERVICE_NAME}:${BUILD_ID} .
	make deploy

deploy-ezee: EZEE_ENV=dev-ezee
deploy-ezee: KEYCLOAK_REALM_URL=https://demoauth.ezeefin.net.in/realms/ezee
deploy-ezee: INTROSPECTION_URL=https://dev-ezee-vega-iam-agxygoruoq-as.a.run.app/vegaspread/api/v1/iam/token/introspect
deploy-ezee: OIDC_CLIENT_ID=ezee
deploy-ezee:
	docker pull ${GCR_REPO}/${ENV}-${SERVICE_NAME}
	docker tag ${GCR_REPO}/${ENV}-${SERVICE_NAME} ${GCR_REPO}/${EZEE_ENV}-${SERVICE_NAME}:latest

	gcloud artifacts docker images delete ${GCR_REPO}/${EZEE_ENV}-${SERVICE_NAME} --quiet || true
	docker push ${GCR_REPO}/${EZEE_ENV}-${SERVICE_NAME}:latest

	gcloud run deploy ${EZEE_ENV}-vega-${SERVICE_NAME} \
		--image ${GCR_REPO}/${EZEE_ENV}-${SERVICE_NAME} \
		--allow-unauthenticated --port=8080 --cpu=${GCLOUD_RUN_CPU} --memory=${GCLOUD_RUN_MEM} \
		--max-instances=${GCLOUD_RUN_MAX_INSTANCES} --concurrency ${GCLOUD_RUN_CONCURRENCY} \
		--region=asia-southeast1 --cpu-boost --cpu-throttling \
		--set-env-vars VEGA_ENV=${EZEE_ENV} \
		--set-env-vars CLOUD_SQL_INSTANCE=${CLOUD_SQL_INSTANCE} \
		--set-env-vars QUARKUS_DATASOURCE_USERNAME=${ENV}-vegaspread \
		--set-env-vars QUARKUS_DATASOURCE_JDBC_URL=jdbc:mysql:///${ENV}_vega_${SERVICE_NAME} \
		--set-env-vars QUARKUS_OIDC_AUTH_SERVER_URL=${KEYCLOAK_REALM_URL} \
		--set-env-vars QUARKUS_OIDC_DISCOVERY_ENABLED=false \
		--set-env-vars QUARKUS_OIDC_INTROSPECTION_PATH=${INTROSPECTION_URL} \
		--set-env-vars QUARKUS_OIDC_CLIENT_ID=${OIDC_CLIENT_ID} \
		--set-env-vars QUARKUS_OIDC_TOKEN_REQUIRE_JWT_INTROSPECTION_ONLY=true \
		--set-env-vars VEGA_CLOUD_PROVIDER=gcp \
		--project=vegaspread-7586a --service-account=${ENV}-<EMAIL>

deploy-jvm:
	QUARKUS_SMALLRYE_OPENAPI_OAUTH2_IMPLICIT_AUTHORIZATION_URL=https://auth.vegaspread.cloud/realms/dev-vega/protocol/openid-connect/auth mvn clean package -Pjvm,gcp
	docker build -f src/main/docker/Dockerfile.jvm -t thewalnutai/vega-${SERVICE_NAME}:${BUILD_ID} .
	make deploy

deploy-native-aws:
	mvn clean package -Pnative,aws
	aws s3 cp target/function.zip s3://${LAMBDA_S3_BUCKET_NAME}/${SERVICE_NAME}-lambda.zip
	@echo "Updating Lambda function..."
	aws lambda update-function-code \
		--function-name ${ENV}-${SERVICE_NAME}-lambda \
		--s3-bucket ${LAMBDA_S3_BUCKET_NAME} \
		--s3-key ${SERVICE_NAME}-lambda.zip
	@echo "Waiting for Lambda update to complete..."
	aws lambda wait function-updated --function-name ${ENV}-${SERVICE_NAME}-lambda
	@echo "Lambda function updated successfully."

verify-lambda:
	@echo "Verifying Lambda function update..."
	@echo "Last update time (local):"
	@aws lambda get-function \
		--function-name ${ENV}-${SERVICE_NAME}-lambda \
		--query 'Configuration.LastModified' \
		--output text | xargs -I{} date -d {} "+%Y-%m-%d %H:%M:%S local time"
	@echo "Revision ID:"
	@aws lambda get-function \
		--function-name ${ENV}-${SERVICE_NAME}-lambda \
		--query 'Configuration.RevisionId' \
		--output text

test:
	mvn clean package -Pnative,aws
	sam local start-api --template ./sam.native.yaml
