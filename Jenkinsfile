pipeline {
    agent any

    tools {
        maven "maven"
    }

    environment {
        ENV = "${env.JOB_NAME.split('-')[0].toLowerCase()}"
        SERVICE_NAME = "${env.JOB_NAME.split('-')[1].toLowerCase()}"
        ENV_UPPER = "${ENV.toUpperCase()}"
        GCLOUD_RUN_CPU = "1"
        GCLOUD_RUN_MEM = "768Mi"
        GCLOUD_RUN_CONCURRENCY = "80"
        GCLOUD_RUN_MAX_INSTANCES = "1"
        BUILD_DATE = sh(script: 'date "+%d-%m-%y"', returnStdout: true).trim()
        BUILD_ID = "${ENV}-${BUILD_DATE}-${env.BUILD_NUMBER}"
        CLOUD_SQL_INSTANCE = "vegaspread-7586a:asia-southeast1:vega-db"
    }

    stages {
        stage("Set infrastructure variables") {
            steps {
                script {
                    if (ENV == 'dev') {
                        GCLOUD_RUN_MAX_INSTANCES = "1"
                        KEYCLOAK_REALM = "dev-vega"
                        QUARKUS_REST_CLIENT_RUNPOD_URL = "https://api.runpod.ai/v2/7a31shtmkg3sa0"
                    } else {
                        GCLOUD_RUN_MAX_INSTANCES = "5"
                        KEYCLOAK_REALM = "vegaspread"
                        QUARKUS_REST_CLIENT_RUNPOD_URL = "https://api.runpod.ai/v2/l63nsvphd32tj2"
                    }
                }
            }
        }

        stage("Build with Maven") {
            steps {
				withCredentials([file(credentialsId: 'GCP_CREDS', variable: "GCP_CREDS")]){
					sh """
						gcloud auth activate-service-account --key-file="${GCP_CREDS}"
						export GOOGLE_APPLICATION_CREDENTIALS="${GCP_CREDS}"
						export QUARKUS_SMALLRYE_OPENAPI_OAUTH2_IMPLICIT_AUTHORIZATION_URL='https://auth.vegaspread.cloud/realms/${KEYCLOAK_REALM}/protocol/openid-connect/auth' && \
						mvn clean package -Pnative,gcp
					"""
				}
            }
        }

        stage("Build amd64 Docker Image") {
            steps {
                sh """
                docker build -f src/main/docker/Dockerfile.native-micro -t thewalnutai/vega-${SERVICE_NAME}:${BUILD_ID} .
                """
            }
        }

        stage("Push to docker hub") {
            steps {
                withDockerRegistry([credentialsId: "DOCKERHUB_CREDENTIALS", "url": ""]) {
                    sh """
                        docker push thewalnutai/vega-${SERVICE_NAME}:${BUILD_ID}
                        docker tag thewalnutai/vega-${SERVICE_NAME}:${BUILD_ID} thewalnutai/vega-${SERVICE_NAME}:${ENV}
                        docker push thewalnutai/vega-${SERVICE_NAME}:${ENV}
                    """
                }
            }
        }

        stage("Push to GCP Artifact Registry"){
            steps{
                withCredentials([file(credentialsId: 'GCP_CREDS', variable: "GCP_CREDS")]){
                    sh """
                        gcloud auth activate-service-account --key-file="${GCP_CREDS}"
                        gcloud auth configure-docker asia-southeast1-docker.pkg.dev
                        gcloud artifacts docker images delete asia-southeast1-docker.pkg.dev/vegaspread-7586a/java/${ENV}-${SERVICE_NAME} --quiet || true
                        docker tag thewalnutai/vega-${SERVICE_NAME}:${BUILD_ID} asia-southeast1-docker.pkg.dev/vegaspread-7586a/java/${ENV}-${SERVICE_NAME}
                        docker push asia-southeast1-docker.pkg.dev/vegaspread-7586a/java/${ENV}-${SERVICE_NAME}
                    """
                }
            }
        }

        stage("Deploy to Cloud Run"){
            steps{
                withCredentials([file(credentialsId: 'GCP_CREDS', variable: "GCP_CREDS")]){
                    sh """
                        gcloud auth activate-service-account --key-file="${GCP_CREDS}"
                        gcloud run deploy ${ENV}-vega-${SERVICE_NAME} \
                            --image asia-southeast1-docker.pkg.dev/vegaspread-7586a/java/${ENV}-${SERVICE_NAME} \
                            --allow-unauthenticated --port=8080 --cpu=${GCLOUD_RUN_CPU} --memory=${GCLOUD_RUN_MEM} \
                            --max-instances=${GCLOUD_RUN_MAX_INSTANCES} --concurrency ${GCLOUD_RUN_CONCURRENCY} \
                            --region=asia-southeast1 --cpu-boost --cpu-throttling \
                            --set-env-vars VEGA_ENV=${ENV} \
                            --set-env-vars CLOUD_SQL_INSTANCE=${CLOUD_SQL_INSTANCE} \
                            --set-env-vars QUARKUS_OIDC_AUTH_SERVER_URL=https://auth.vegaspread.cloud/realms/${KEYCLOAK_REALM} \
                            --set-env-vars QUARKUS_REST_CLIENT_RUNPOD_URL=${QUARKUS_REST_CLIENT_RUNPOD_URL} \
                            --set-env-vars VEGA_API_CLIENT_NAME=${ENV}-api-client \
                            --set-env-vars BUCKET_NAME=${ENV}-vegaspread \
                            --update-secrets=RUNPOD_API_KEY=RUNPOD_API_KEY:latest \
                            --update-secrets=WISE_CLIENT_SECRET=${ENV_UPPER}_WISE_API_KEY:latest \
							--update-secrets=QUARKUS_MAILER_PASSWORD=${ENV_UPPER}_SMTP_PASSWORD:latest \
                            --project=vegaspread-7586a --service-account=${ENV}-<EMAIL>
                    """
                }
            }
        }

        stage("Cleanup"){
            steps{
                withCredentials([file(credentialsId: 'GCP_CREDS', variable: "GCP_CREDS")]){
                    sh """
                        gcloud auth activate-service-account --key-file="${GCP_CREDS}"
                        docker rmi asia-southeast1-docker.pkg.dev/vegaspread-7586a/java/${ENV}-${SERVICE_NAME} \
                            thewalnutai/vega-${SERVICE_NAME}:${BUILD_ID} thewalnutai/vega-${SERVICE_NAME}:${ENV}
                    """
                }
            }
        }
    }
}
